<!DOCTYPE html>
<html>
<head>
    <title>Fix Department Duplicates</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #faf2e9; }
        .status-box { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        .error { background: #ffebee; border-color: #f44336; }
        .warning { background: #fff3e0; border-color: #ff9800; }
        .info { background: #e3f2fd; border-color: #2196f3; }
        .btn { background: #f15b31; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
        .page-title { background-color: #f15b31; color: white; padding: 20px; border-radius: 5px; text-align: center; margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f15b31; color: white; }
        .duplicate { background-color: #ffebee; }
        .original { background-color: #e8f5e8; }
    </style>
</head>
<body>
    <h1 class="page-title">🔧 Fix Department Duplicates</h1>
    
    <?php
    require_once 'app/config/config.php';
    
    echo "<div class='warning-box'>";
    echo "<h3>🔍 Checking All Departments for Duplicates</h3>";
    echo "<p>Let me see all your departments and remove the duplicates properly...</p>";
    echo "</div>";
    
    try {
        // Get all departments
        $all_dept_sql = "SELECT * FROM departments ORDER BY name, id";
        $all_dept_stmt = $conn->prepare($all_dept_sql);
        $all_dept_stmt->execute();
        $all_departments = $all_dept_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='info-box'>";
        echo "<h3>📋 All Departments Currently in Database:</h3>";
        echo "<table>";
        echo "<tr><th>ID</th><th>Name</th><th>Description</th><th>Created</th><th>Status</th></tr>";
        
        $department_groups = [];
        $duplicates_to_remove = [];
        
        // Group departments by name to find duplicates
        foreach ($all_departments as $dept) {
            $name = $dept['name'];
            if (!isset($department_groups[$name])) {
                $department_groups[$name] = [];
            }
            $department_groups[$name][] = $dept;
        }
        
        // Identify duplicates
        foreach ($all_departments as $dept) {
            $name = $dept['name'];
            $group = $department_groups[$name];
            
            if (count($group) > 1) {
                // This is a duplicate group
                // Keep the first one (lowest ID), mark others for removal
                $first_dept = $group[0];
                if ($dept['id'] == $first_dept['id']) {
                    // This is the original (keep)
                    echo "<tr class='original'>";
                    echo "<td>" . $dept['id'] . "</td>";
                    echo "<td>" . htmlspecialchars($dept['name']) . "</td>";
                    echo "<td>" . htmlspecialchars($dept['description'] ?? 'N/A') . "</td>";
                    echo "<td>" . ($dept['created_at'] ?? 'N/A') . "</td>";
                    echo "<td>✅ KEEP (Original)</td>";
                    echo "</tr>";
                } else {
                    // This is a duplicate (remove)
                    echo "<tr class='duplicate'>";
                    echo "<td>" . $dept['id'] . "</td>";
                    echo "<td>" . htmlspecialchars($dept['name']) . "</td>";
                    echo "<td>" . htmlspecialchars($dept['description'] ?? 'N/A') . "</td>";
                    echo "<td>" . ($dept['created_at'] ?? 'N/A') . "</td>";
                    echo "<td>🗑️ REMOVE (Duplicate)</td>";
                    echo "</tr>";
                    $duplicates_to_remove[] = $dept;
                }
            } else {
                // No duplicates
                echo "<tr class='original'>";
                echo "<td>" . $dept['id'] . "</td>";
                echo "<td>" . htmlspecialchars($dept['name']) . "</td>";
                echo "<td>" . htmlspecialchars($dept['description'] ?? 'N/A') . "</td>";
                echo "<td>" . ($dept['created_at'] ?? 'N/A') . "</td>";
                echo "<td>✅ KEEP (Unique)</td>";
                echo "</tr>";
            }
        }
        echo "</table>";
        echo "</div>";
        
        // Remove duplicates
        if (count($duplicates_to_remove) > 0) {
            echo "<div class='warning-box'>";
            echo "<h3>🗑️ Removing " . count($duplicates_to_remove) . " Duplicate Departments</h3>";
            
            $conn->beginTransaction();
            $removed_count = 0;
            
            foreach ($duplicates_to_remove as $dept) {
                try {
                    $delete_sql = "DELETE FROM departments WHERE id = ?";
                    $delete_stmt = $conn->prepare($delete_sql);
                    $delete_stmt->execute([$dept['id']]);
                    
                    echo "<p style='color: green;'>✅ Removed duplicate: " . htmlspecialchars($dept['name']) . " (ID: " . $dept['id'] . ")</p>";
                    $removed_count++;
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Error removing " . htmlspecialchars($dept['name']) . " (ID: " . $dept['id'] . "): " . $e->getMessage() . "</p>";
                }
            }
            
            $conn->commit();
            echo "<p><strong>Total removed:</strong> $removed_count duplicates</p>";
            echo "</div>";
        } else {
            echo "<div class='success-box'>";
            echo "<h3>✅ No Duplicates Found</h3>";
            echo "<p>All departments are unique!</p>";
            echo "</div>";
        }
        
        // Show final clean departments
        echo "<div class='success-box'>";
        echo "<h3>🎉 Final Clean Departments List</h3>";
        
        $final_dept_sql = "SELECT * FROM departments ORDER BY name";
        $final_dept_stmt = $conn->prepare($final_dept_sql);
        $final_dept_stmt->execute();
        $final_departments = $final_dept_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>ID</th><th>Name</th><th>Description</th></tr>";
        foreach ($final_departments as $dept) {
            echo "<tr>";
            echo "<td>" . $dept['id'] . "</td>";
            echo "<td>" . htmlspecialchars($dept['name']) . "</td>";
            echo "<td>" . htmlspecialchars($dept['description'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p><strong>Total unique departments:</strong> " . count($final_departments) . "</p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error-box'>";
        echo "<h3>❌ Error</h3>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    echo "<div class='info-box'>";
    echo "<h3>🎯 Next Steps</h3>";
    echo "<p>Now check your departments in phpMyAdmin to confirm they're clean!</p>";
    echo "<a href='http://localhost/phpmyadmin' class='btn' target='_blank'>🔍 Open phpMyAdmin</a>";
    echo "<a href='app/modules/reports/reports.php' class='btn'>📊 Test Reports</a>";
    echo "</div>";
    
    echo "<div class='success-box'>";
    echo "<h3>🙏 Sorry Again!</h3>";
    echo "<p>Sorry par for the duplicate departments! I should have been more careful.</p>";
    echo "<p>Now your departments table should be clean with no duplicates.</p>";
    echo "</div>";
    ?>
    
    <script>
        console.log('🔧 Department duplicates cleanup complete');
    </script>
</body>
</html>
