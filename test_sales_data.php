<?php
require_once 'app/config/config.php';

echo "<h2>Sales Data Test</h2>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    .stat-card { background: linear-gradient(45deg, #f15b31, #d14426); color: white; padding: 20px; border-radius: 10px; text-align: center; margin: 10px; display: inline-block; min-width: 200px; }
    .stat-card h6 { margin-bottom: 10px; font-size: 14px; opacity: 0.9; }
    .stat-card h3 { margin: 0; font-size: 28px; font-weight: bold; }
</style>";

// Set up date range variables for widgets
$report_type = 'monthly';
$date_range = date('Y-m-d');
$start_date = date('Y-m-01', strtotime($date_range));
$end_date = date('Y-m-t', strtotime($date_range));
$group_by = "DATE_FORMAT(created_at, '%Y-%m')";

echo "<div class='test-section'>";
echo "<h3>1. Date Range Test</h3>";
echo "<p>Start Date: $start_date</p>";
echo "<p>End Date: $end_date</p>";
echo "<p>Group By: $group_by</p>";
echo "</div>";

echo "<div class='test-section'>";
echo "<h3>2. Raw Sales Query Test</h3>";

try {
    $sales_sql = "SELECT
        " . $group_by . " as period,
        COUNT(*) as total_orders,
        SUM(total_amount) as total_sales,
        AVG(total_amount) as average_order_value
      FROM procurement_orders
      WHERE created_at BETWEEN :start_date AND :end_date
      AND status = 'completed'
      GROUP BY " . $group_by . "
      ORDER BY period DESC";

    $sales_stmt = $conn->prepare($sales_sql);
    $sales_stmt->bindParam(':start_date', $start_date);
    $end_date_with_time = $end_date . ' 23:59:59';
    $sales_stmt->bindParam(':end_date', $end_date_with_time);
    $sales_stmt->execute();
    $sales_data = $sales_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='success'>";
    echo "<p>✅ Query executed successfully!</p>";
    echo "<p>Found " . count($sales_data) . " sales periods</p>";
    echo "<pre>" . json_encode($sales_data, JSON_PRETTY_PRINT) . "</pre>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<p>❌ Query failed: " . $e->getMessage() . "</p>";
    echo "</div>";
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h3>3. All Completed Orders Test</h3>";

try {
    $all_orders_sql = "SELECT 
        order_number, 
        total_amount, 
        status, 
        created_at 
        FROM procurement_orders 
        WHERE status = 'completed' 
        ORDER BY created_at DESC 
        LIMIT 10";
    
    $stmt = $conn->prepare($all_orders_sql);
    $stmt->execute();
    $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='success'>";
    echo "<p>✅ Found " . count($orders) . " completed orders</p>";
    echo "<table border='1' style='width: 100%; border-collapse: collapse;'>";
    echo "<tr><th>Order Number</th><th>Amount</th><th>Status</th><th>Date</th></tr>";
    
    $total_all = 0;
    foreach ($orders as $order) {
        echo "<tr>";
        echo "<td>" . $order['order_number'] . "</td>";
        echo "<td>$" . number_format($order['total_amount'], 2) . "</td>";
        echo "<td>" . $order['status'] . "</td>";
        echo "<td>" . $order['created_at'] . "</td>";
        echo "</tr>";
        $total_all += $order['total_amount'];
    }
    echo "</table>";
    echo "<p><strong>Total from these orders: $" . number_format($total_all, 2) . "</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h3>4. Sales Cards Preview</h3>";

// Calculate totals
$total = 0;
$orders = 0;
foreach ($sales_data as $row) {
    $total += $row['total_sales'];
    $orders += $row['total_orders'];
}

echo "<div style='display: flex; gap: 20px; flex-wrap: wrap;'>";

echo "<div class='stat-card'>";
echo "<h6>Total Sales</h6>";
echo "<h3>$" . number_format($total, 2) . "</h3>";
echo "</div>";

echo "<div class='stat-card'>";
echo "<h6>Total Orders</h6>";
echo "<h3>" . number_format($orders) . "</h3>";
echo "</div>";

echo "<div class='stat-card'>";
echo "<h6>Average Order Value</h6>";
echo "<h3>$" . ($orders > 0 ? number_format($total / $orders, 2) : '0.00') . "</h3>";
echo "</div>";

echo "</div>";

if ($total == 0) {
    echo "<div class='error'>";
    echo "<h4>❌ Problem: Sales cards showing $0.00</h4>";
    echo "<p>This could be because:</p>";
    echo "<ul>";
    echo "<li>No completed orders in the current month ($start_date to $end_date)</li>";
    echo "<li>Orders exist but with different date range</li>";
    echo "<li>Orders exist but status is not 'completed'</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div class='success'>";
    echo "<h4>✅ Sales data looks good!</h4>";
    echo "</div>";
}

echo "</div>";

echo "<div class='test-section'>";
echo "<h3>5. Quick Links</h3>";
echo "<p><a href='app/modules/inventory/inventory_dashboard.php'>Test Inventory Dashboard</a></p>";
echo "<p><a href='app/modules/admin/admin_dashboard.php'>Admin Dashboard</a></p>";
echo "<p><a href='verify_data.php'>Verify All Data</a></p>";
echo "</div>";
?>
