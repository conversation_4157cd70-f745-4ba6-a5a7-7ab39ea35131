<?php
require_once '../config/config.php';
header('Content-Type: application/json');

try {
    // Try to get data from inventory table first
    $sql = "SELECT brand_name, SUM(stocks) as total_stock, COUNT(*) as total_products FROM inventory WHERE status = 'approved' GROUP BY brand_name ORDER BY total_stock DESC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $data = [];
    if (count($result) > 0) {
        foreach ($result as $row) {
            $data[] = [
                'brand_name' => $row['brand_name'],
                'total_stock' => (int)$row['total_stock'],
                'total_products' => (int)$row['total_products']
            ];
        }
    } else {
        // Fallback to products table if inventory is empty
        $sql = "SELECT name as brand_name, SUM(stock_quantity) as total_stock, COUNT(*) as total_products FROM products GROUP BY category_id ORDER BY total_stock DESC";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($result) > 0) {
            foreach ($result as $row) {
                $data[] = [
                    'brand_name' => $row['brand_name'],
                    'total_stock' => (int)$row['total_stock'],
                    'total_products' => (int)$row['total_products']
                ];
            }
        }
    }
    
    echo json_encode($data);
} catch (Exception $e) {
    echo json_encode([]);
}
?>
