<!DOCTYPE html>
<html>
<head>
    <title>Insert Sample Data to Database</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #faf2e9; }
        .status-box { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        .error { background: #ffebee; border-color: #f44336; }
        .warning { background: #fff3e0; border-color: #ff9800; }
        .info { background: #e3f2fd; border-color: #2196f3; }
        .btn { background: #f15b31; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
        .page-title { background-color: #f15b31; color: white; padding: 20px; border-radius: 5px; text-align: center; margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f15b31; color: white; }
    </style>
</head>
<body>
    <h1 class="page-title">🔧 Insert Sample Data to Database</h1>
    
    <?php
    require_once 'app/config/config.php';
    
    echo "<div class='info-box'>";
    echo "<h3>📋 What I'll Insert:</h3>";
    echo "<p>I'll insert all the sample data from the reports into your actual database tables:</p>";
    echo "<ul>";
    echo "<li><strong>Sales Data:</strong> 5 sample orders in procurement_orders table</li>";
    echo "<li><strong>Inventory Data:</strong> 6 sample products in inventory table</li>";
    echo "<li><strong>Users Data:</strong> Sample customers for the orders</li>";
    echo "<li><strong>Departments Data:</strong> Sample departments if needed</li>";
    echo "</ul>";
    echo "</div>";
    
    $success_count = 0;
    $error_count = 0;
    $messages = [];
    
    try {
        // Start transaction
        $conn->beginTransaction();
        
        echo "<div class='status-box'>";
        echo "<h3>🔄 Inserting Sample Data...</h3>";
        
        // 1. Insert sample departments if they don't exist
        echo "<p><strong>1. Checking/Inserting Departments...</strong></p>";
        $departments = ['Finance', 'Procurement', 'Logistics', 'Inventory', 'Sales'];
        
        foreach ($departments as $dept) {
            try {
                $check_dept = $conn->prepare("SELECT id FROM departments WHERE name = ?");
                $check_dept->execute([$dept]);
                
                if ($check_dept->rowCount() == 0) {
                    $insert_dept = $conn->prepare("INSERT INTO departments (name, description, created_at) VALUES (?, ?, NOW())");
                    $insert_dept->execute([$dept, $dept . ' Department', ]);
                    echo "<span style='color: green;'>✅ Added department: $dept</span><br>";
                    $success_count++;
                } else {
                    echo "<span style='color: blue;'>ℹ️ Department already exists: $dept</span><br>";
                }
            } catch (Exception $e) {
                echo "<span style='color: red;'>❌ Error adding department $dept: " . $e->getMessage() . "</span><br>";
                $error_count++;
            }
        }
        
        // 2. Insert sample users if they don't exist
        echo "<p><strong>2. Checking/Inserting Sample Users...</strong></p>";
        $users = [
            ['John Smith', '<EMAIL>', 'Finance'],
            ['Maria Garcia', '<EMAIL>', 'Procurement'],
            ['Robert Johnson', '<EMAIL>', 'Logistics'],
            ['Lisa Chen', '<EMAIL>', 'Inventory'],
            ['David Wilson', '<EMAIL>', 'Sales']
        ];
        
        foreach ($users as $user) {
            try {
                $check_user = $conn->prepare("SELECT id FROM users WHERE email = ?");
                $check_user->execute([$user[1]]);
                
                if ($check_user->rowCount() == 0) {
                    $insert_user = $conn->prepare("INSERT INTO users (name, email, department, password, created_at) VALUES (?, ?, ?, ?, NOW())");
                    $insert_user->execute([$user[0], $user[1], $user[2], password_hash('password123', PASSWORD_DEFAULT)]);
                    echo "<span style='color: green;'>✅ Added user: {$user[0]} ({$user[2]})</span><br>";
                    $success_count++;
                } else {
                    echo "<span style='color: blue;'>ℹ️ User already exists: {$user[0]}</span><br>";
                }
            } catch (Exception $e) {
                echo "<span style='color: red;'>❌ Error adding user {$user[0]}: " . $e->getMessage() . "</span><br>";
                $error_count++;
            }
        }
        
        // 3. Insert sample procurement orders
        echo "<p><strong>3. Inserting Sample Procurement Orders...</strong></p>";
        $orders = [
            ['ORD-2024-001', 324.75, 'completed', '<EMAIL>'],
            ['ORD-2024-002', 134.85, 'completed', '<EMAIL>'],
            ['ORD-2024-003', 124.95, 'completed', '<EMAIL>'],
            ['ORD-2024-004', 224.70, 'completed', '<EMAIL>'],
            ['ORD-2024-005', 139.80, 'completed', '<EMAIL>']
        ];
        
        foreach ($orders as $index => $order) {
            try {
                $check_order = $conn->prepare("SELECT id FROM procurement_orders WHERE order_number = ?");
                $check_order->execute([$order[0]]);
                
                if ($check_order->rowCount() == 0) {
                    $order_date = date('Y-m-d H:i:s', strtotime("-$index days"));
                    $insert_order = $conn->prepare("INSERT INTO procurement_orders (order_number, total_amount, status, customer_email, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)");
                    $insert_order->execute([$order[0], $order[1], $order[2], $order[3], $order_date, $order_date]);
                    echo "<span style='color: green;'>✅ Added order: {$order[0]} - $" . number_format($order[1], 2) . "</span><br>";
                    $success_count++;
                } else {
                    echo "<span style='color: blue;'>ℹ️ Order already exists: {$order[0]}</span><br>";
                }
            } catch (Exception $e) {
                echo "<span style='color: red;'>❌ Error adding order {$order[0]}: " . $e->getMessage() . "</span><br>";
                $error_count++;
            }
        }
        
        // 4. Insert sample inventory products
        echo "<p><strong>4. Inserting Sample Inventory Products...</strong></p>";
        $products = [
            ['Organic Turmeric Powder', 'EuroSpice', 12.99, 50, '250g', 'Pouch', '2025-12-31', 'India', 'TUR-2023-001'],
            ['Premium Cinnamon Sticks', 'EuroSpice', 8.99, 75, '100g', 'Box', '2026-06-30', 'Sri Lanka', 'CIN-2023-002'],
            ['Black Peppercorns', 'EuroSpice', 7.49, 100, '150g', 'Jar', '2025-09-15', 'Vietnam', 'PEP-2023-003'],
            ['Saffron Threads', 'VISKASE', 24.99, 30, '5g', 'Jar', '2027-03-31', 'Spain', 'SAF-2023-006'],
            ['Cumin Seeds', 'VISKASE', 6.99, 120, '100g', 'Pouch', '2026-01-31', 'India', 'CUM-2023-007'],
            ['Paprika Powder', 'SpiceWorld', 5.99, 110, '150g', 'Pouch', '2025-12-31', 'Hungary', 'PAP-2023-009']
        ];
        
        foreach ($products as $product) {
            try {
                $check_product = $conn->prepare("SELECT id FROM inventory WHERE prod_name = ? AND brand_name = ?");
                $check_product->execute([$product[0], $product[1]]);
                
                if ($check_product->rowCount() == 0) {
                    $insert_product = $conn->prepare("INSERT INTO inventory (prod_name, brand_name, price, stocks, prod_measure, pack_type, expiry_date, country, batch_code, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'approved', NOW())");
                    $insert_product->execute($product);
                    echo "<span style='color: green;'>✅ Added product: {$product[0]} ({$product[1]}) - $" . number_format($product[2], 2) . "</span><br>";
                    $success_count++;
                } else {
                    echo "<span style='color: blue;'>ℹ️ Product already exists: {$product[0]} ({$product[1]})</span><br>";
                }
            } catch (Exception $e) {
                echo "<span style='color: red;'>❌ Error adding product {$product[0]}: " . $e->getMessage() . "</span><br>";
                $error_count++;
            }
        }
        
        // Commit transaction
        $conn->commit();
        echo "</div>";
        
        echo "<div class='success-box'>";
        echo "<h3>🎉 Sample Data Insertion Complete!</h3>";
        echo "<p><strong>Results:</strong></p>";
        echo "<ul>";
        echo "<li>✅ <strong>Successful insertions:</strong> $success_count</li>";
        echo "<li>❌ <strong>Errors:</strong> $error_count</li>";
        echo "</ul>";
        echo "</div>";
        
    } catch (Exception $e) {
        $conn->rollback();
        echo "</div>";
        echo "<div class='error-box'>";
        echo "<h3>❌ Transaction Failed</h3>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    // Now update the reports to use real database data instead of hardcoded data
    echo "<div class='info-box'>";
    echo "<h3>🔄 Next Step: Update Reports to Use Real Database Data</h3>";
    echo "<p>Now that we have real data in the database, I can update the reports to fetch from database instead of using hardcoded sample data.</p>";
    echo "<a href='update_reports_to_use_database.php' class='btn'>🔧 Update Reports to Use Database</a>";
    echo "</div>";
    
    echo "<div class='status-box'>";
    echo "<h3>🎯 Test Your Data</h3>";
    echo "<p>You can now test the reports with real database data:</p>";
    echo "<a href='app/modules/reports/reports.php' class='btn'>📊 Test Reports</a>";
    echo "<a href='debug_reports_tables.php' class='btn'>🔍 Debug Database Data</a>";
    echo "</div>";
    ?>
    
    <script>
        console.log('🔧 Sample data insertion complete');
    </script>
</body>
</html>
