<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Inventory Chart</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
    <style>
        .chart-container { height: 400px; }
        .stats-card { background: linear-gradient(45deg, #4b6cb7, #182848); color: white; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Inventory Chart Test</h1>
        
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Total Products</h5>
                        <h2 id="totalProductsCard">Loading...</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Total Stocks</h5>
                        <h2 id="totalStocksCard">Loading...</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <h5 class="card-title">API Status</h5>
                        <h2 id="apiStatus">Testing...</h2>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Inventory by Brand Chart</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="inventoryChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Brand Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="brandInventoryTable">
                                <thead>
                                    <tr>
                                        <th>Brand</th>
                                        <th>Products</th>
                                        <th>Stock</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr><td colspan="3" class="text-center">Loading...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Debug Information</h5>
                    </div>
                    <div class="card-body">
                        <div id="debugInfo">
                            <p>Testing API endpoints...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const inventoryCtx = document.getElementById('inventoryChart').getContext('2d');
        let inventoryChart;
        
        function addDebugInfo(message) {
            const debugDiv = document.getElementById('debugInfo');
            debugDiv.innerHTML += '<p>' + new Date().toLocaleTimeString() + ': ' + message + '</p>';
        }
        
        async function testInventoryAPI() {
            addDebugInfo('Starting API test...');
            
            try {
                // Test the API endpoint
                const response = await fetch('app/modules/get_inventory_data.php');
                addDebugInfo('API Response status: ' + response.status);
                
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.status);
                }
                
                const data = await response.json();
                addDebugInfo('API returned ' + data.length + ' records');
                addDebugInfo('Raw data: ' + JSON.stringify(data));
                
                if (data.length === 0) {
                    addDebugInfo('⚠️ No data returned from API');
                    document.getElementById('apiStatus').textContent = 'No Data';
                    return;
                }
                
                // Process the data
                const brands = data.map(item => item.brand_name);
                const stocks = data.map(item => item.total_stock);
                const products = data.map(item => item.total_products);
                
                // Calculate totals
                const totalAllStocks = stocks.reduce((a, b) => a + b, 0);
                const totalAllProducts = products.reduce((a, b) => a + b, 0);
                
                // Update cards
                document.getElementById('totalProductsCard').textContent = totalAllProducts;
                document.getElementById('totalStocksCard').textContent = totalAllStocks;
                document.getElementById('apiStatus').textContent = '✅ OK';
                
                // Create chart
                if (inventoryChart) {
                    inventoryChart.destroy();
                }
                
                inventoryChart = new Chart(inventoryCtx, {
                    type: 'pie',
                    data: {
                        labels: brands,
                        datasets: [{
                            data: stocks,
                            backgroundColor: [
                                '#4b6cb7', '#182848', '#2c3e50', '#3498db', '#2980b9', '#e67e22', '#16a085', '#8e44ad', '#c0392b'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const idx = context.dataIndex;
                                        return brands[idx] + ': ' + stocks[idx] + ' stocks, ' + products[idx] + ' products';
                                    }
                                }
                            }
                        }
                    }
                });
                
                // Update table
                const tbody = document.querySelector('#brandInventoryTable tbody');
                tbody.innerHTML = '';
                data.forEach(item => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `<td>${item.brand_name}</td><td>${item.total_products}</td><td>${item.total_stock}</td>`;
                    tbody.appendChild(tr);
                });
                
                addDebugInfo('✅ Chart and table updated successfully');
                
            } catch (error) {
                addDebugInfo('❌ Error: ' + error.message);
                document.getElementById('apiStatus').textContent = '❌ Error';
                document.getElementById('totalProductsCard').textContent = 'Error';
                document.getElementById('totalStocksCard').textContent = 'Error';
            }
        }
        
        // Run the test
        testInventoryAPI();
    </script>
</body>
</html>
