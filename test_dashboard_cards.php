<?php
echo "<h2>Test Dashboard Cards Data</h2>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .card { margin: 15px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    .stats-card { background: linear-gradient(45deg, #4b6cb7, #182848); color: white; padding: 20px; text-align: center; border-radius: 10px; margin: 10px; }
</style>";

try {
    require_once 'app/config/config.php';
    
    // Test the same function as admin dashboard
    function getModuleStats($conn, $module)
    {
        $stats = [];
        try {
            switch ($module) {
                case 'users':
                    $query = "SELECT COUNT(*) as total FROM users";
                    $stmt = $conn->prepare($query);
                    $stmt->execute();
                    $result = $stmt->fetch(PDO::FETCH_ASSOC);
                    $stats['total_users'] = $result['total'] ?? 0;
                    break;
                case 'products':
                    $query = "SELECT COUNT(*) as total FROM products";
                    $stmt = $conn->prepare($query);
                    $stmt->execute();
                    $result = $stmt->fetch(PDO::FETCH_ASSOC);
                    $stats['total_products'] = $result['total'] ?? 0;
                    break;
                case 'inventory':
                    $query = "SELECT COUNT(*) as total FROM inventory WHERE status = 'approved'";
                    $stmt = $conn->prepare($query);
                    $stmt->execute();
                    $result = $stmt->fetch(PDO::FETCH_ASSOC);
                    $stats['total_items'] = $result['total'] ?? 0;
                    break;
            }
        } catch (Exception $e) {
            echo "<p>Error in $module: " . $e->getMessage() . "</p>";
            switch ($module) {
                case 'users':
                    $stats['total_users'] = 0;
                    break;
                case 'products':
                    $stats['total_products'] = 0;
                    break;
                case 'inventory':
                    $stats['total_items'] = 0;
                    break;
            }
        }
        return $stats;
    }
    
    $moduleStats = [
        'users' => getModuleStats($conn, 'users'),
        'products' => getModuleStats($conn, 'products'),
        'inventory' => getModuleStats($conn, 'inventory')
    ];
    
    echo "<div class='card success'>";
    echo "<h3>Dashboard Cards Data</h3>";
    echo "<div style='display: flex; gap: 20px;'>";
    
    echo "<div class='stats-card'>";
    echo "<h5>Total Users</h5>";
    echo "<h2>" . ($moduleStats['users']['total_users'] ?? 0) . "</h2>";
    echo "</div>";
    
    echo "<div class='stats-card'>";
    echo "<h5>Total Products</h5>";
    echo "<h2>" . ($moduleStats['products']['total_products'] ?? 0) . "</h2>";
    echo "</div>";
    
    echo "<div class='stats-card'>";
    echo "<h5>Inventory Items</h5>";
    echo "<h2>" . ($moduleStats['inventory']['total_items'] ?? 0) . "</h2>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    
    // Test individual queries
    echo "<div class='card'>";
    echo "<h3>Individual Query Tests</h3>";
    
    $queries = [
        'Users' => "SELECT COUNT(*) as count FROM users",
        'Products' => "SELECT COUNT(*) as count FROM products", 
        'All Inventory' => "SELECT COUNT(*) as count FROM inventory",
        'Approved Inventory' => "SELECT COUNT(*) as count FROM inventory WHERE status = 'approved'",
        'Departments' => "SELECT COUNT(*) as count FROM departments",
        'Categories' => "SELECT COUNT(*) as count FROM categories"
    ];
    
    foreach ($queries as $name => $query) {
        try {
            $stmt = $conn->prepare($query);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<p><strong>$name:</strong> " . $result['count'] . " records</p>";
        } catch (Exception $e) {
            echo "<p><strong>$name:</strong> Error - " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
    
    // Test sample data from each table
    echo "<div class='card'>";
    echo "<h3>Sample Data</h3>";
    
    echo "<h4>Users Sample:</h4>";
    $stmt = $conn->prepare("SELECT username, role, first_name, last_name FROM users LIMIT 3");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($users as $user) {
        echo "<p>• {$user['first_name']} {$user['last_name']} ({$user['username']}) - {$user['role']}</p>";
    }
    
    echo "<h4>Products Sample:</h4>";
    $stmt = $conn->prepare("SELECT name, price FROM products LIMIT 3");
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($products as $product) {
        echo "<p>• {$product['name']} - $" . number_format($product['price'], 2) . "</p>";
    }
    
    echo "<h4>Inventory Sample:</h4>";
    $stmt = $conn->prepare("SELECT prod_name, brand_name, stocks, status FROM inventory LIMIT 3");
    $stmt->execute();
    $inventory = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($inventory as $item) {
        echo "<p>• {$item['prod_name']} ({$item['brand_name']}) - {$item['stocks']} units - {$item['status']}</p>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='card error'>";
    echo "<h3>Database Connection Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<div class='card'>";
echo "<h3>Quick Links</h3>";
echo "<p><a href='app/modules/admin/admin_dashboard.php'>Admin Dashboard</a></p>";
echo "<p><a href='app/modules/inventory/inventory_dashboard.php'>Inventory Dashboard</a></p>";
echo "<p><a href='debug_inventory_api.php'>Debug API</a></p>";
echo "</div>";
?>
