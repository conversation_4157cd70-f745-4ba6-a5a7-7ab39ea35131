<!DOCTYPE html>
<html>
<head>
    <title>Fix Reports with Sample Data</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status-box { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        .error { background: #ffebee; border-color: #f44336; }
        .warning { background: #fff3e0; border-color: #ff9800; }
        .btn { background: #f15b31; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
    </style>
</head>
<body>
    <h1>🔧 Fix Reports with Sample Data</h1>
    
    <?php
    echo "<div class='status-box'>";
    echo "<h3>Forcing Sample Data in Reports</h3>";
    echo "<p>I'll modify the reports to always show sample data so you can see the content...</p>";
    echo "</div>";
    
    // Force sample data in sales report
    $sales_sample_data = "
    // Force sample data for demonstration
    \$transactions_result = [
        ['order_number' => 'ORD-2024-001', 'order_date' => date('Y-m-d H:i:s'), 'customer_name' => 'John <PERSON>', 'department' => 'Finance', 'total_amount' => 324.75, 'status' => 'completed'],
        ['order_number' => 'ORD-2024-002', 'order_date' => date('Y-m-d H:i:s', strtotime('-1 day')), 'customer_name' => 'Maria Garcia', 'department' => 'Procurement', 'total_amount' => 134.85, 'status' => 'completed'],
        ['order_number' => 'ORD-2024-003', 'order_date' => date('Y-m-d H:i:s', strtotime('-2 days')), 'customer_name' => 'Robert Johnson', 'department' => 'Logistics', 'total_amount' => 124.95, 'status' => 'completed'],
        ['order_number' => 'ORD-2024-004', 'order_date' => date('Y-m-d H:i:s', strtotime('-3 days')), 'customer_name' => 'Lisa Chen', 'department' => 'Inventory', 'total_amount' => 224.70, 'status' => 'completed'],
        ['order_number' => 'ORD-2024-005', 'order_date' => date('Y-m-d H:i:s', strtotime('-4 days')), 'customer_name' => 'David Wilson', 'department' => 'Sales', 'total_amount' => 139.80, 'status' => 'completed']
    ];
    
    \$total_sales = array_sum(array_column(\$transactions_result, 'total_amount'));
    \$total_orders = count(\$transactions_result);
    \$avg_order_value = \$total_orders > 0 ? \$total_sales / \$total_orders : 0;
    ";
    
    // Force sample data in inventory report
    $inventory_sample_data = "
    // Force sample data for demonstration
    \$brand_inventory_result = [
        ['brand_name' => 'EuroSpice', 'total_products' => 6, 'total_stock' => 485, 'total_value' => 4847.94, 'average_price' => 9.99],
        ['brand_name' => 'VISKASE', 'total_products' => 4, 'total_stock' => 240, 'total_value' => 2397.96, 'average_price' => 9.99],
        ['brand_name' => 'SpiceWorld', 'total_products' => 2, 'total_stock' => 175, 'total_value' => 1398.25, 'average_price' => 7.99]
    ];
    
    \$inventory_products_result = [
        ['prod_name' => 'Organic Turmeric Powder', 'brand_name' => 'EuroSpice', 'price' => 12.99, 'stocks' => 50, 'prod_measure' => '250g', 'pack_type' => 'Pouch', 'expiry_date' => '2025-12-31', 'country' => 'India', 'batch_code' => 'TUR-2023-001', 'created_at' => '2024-01-15'],
        ['prod_name' => 'Premium Cinnamon Sticks', 'brand_name' => 'EuroSpice', 'price' => 8.99, 'stocks' => 75, 'prod_measure' => '100g', 'pack_type' => 'Box', 'expiry_date' => '2026-06-30', 'country' => 'Sri Lanka', 'batch_code' => 'CIN-2023-002', 'created_at' => '2024-01-20'],
        ['prod_name' => 'Black Peppercorns', 'brand_name' => 'EuroSpice', 'price' => 7.49, 'stocks' => 100, 'prod_measure' => '150g', 'pack_type' => 'Jar', 'expiry_date' => '2025-09-15', 'country' => 'Vietnam', 'batch_code' => 'PEP-2023-003', 'created_at' => '2024-01-25'],
        ['prod_name' => 'Saffron Threads', 'brand_name' => 'VISKASE', 'price' => 24.99, 'stocks' => 30, 'prod_measure' => '5g', 'pack_type' => 'Jar', 'expiry_date' => '2027-03-31', 'country' => 'Spain', 'batch_code' => 'SAF-2023-006', 'created_at' => '2024-02-01'],
        ['prod_name' => 'Cumin Seeds', 'brand_name' => 'VISKASE', 'price' => 6.99, 'stocks' => 120, 'prod_measure' => '100g', 'pack_type' => 'Pouch', 'expiry_date' => '2026-01-31', 'country' => 'India', 'batch_code' => 'CUM-2023-007', 'created_at' => '2024-02-05'],
        ['prod_name' => 'Paprika Powder', 'brand_name' => 'SpiceWorld', 'price' => 5.99, 'stocks' => 110, 'prod_measure' => '150g', 'pack_type' => 'Pouch', 'expiry_date' => '2025-12-31', 'country' => 'Hungary', 'batch_code' => 'PAP-2023-009', 'created_at' => '2024-02-10']
    ];
    ";
    
    // Force sample data in finance report
    $finance_sample_data = "
    // Force sample data for demonstration
    \$dept_expenses_result = [
        ['department' => 'Logistics', 'order_count' => 18, 'total_spent' => 15680.25],
        ['department' => 'Finance', 'order_count' => 15, 'total_spent' => 12450.75],
        ['department' => 'Inventory', 'order_count' => 14, 'total_spent' => 10340.80],
        ['department' => 'Procurement', 'order_count' => 12, 'total_spent' => 8920.50],
        ['department' => 'Sales', 'order_count' => 10, 'total_spent' => 7890.45]
    ];
    
    \$total_expenses = array_sum(array_column(\$dept_expenses_result, 'total_spent'));
    \$total_orders = array_sum(array_column(\$dept_expenses_result, 'order_count'));
    \$pending_orders = 8;
    \$pending_amount = 5420.30;
    \$inventory_value = 8644.15;
    \$inventory_items = 12;
    ";
    
    echo "<div class='status-box success'>";
    echo "<h3>✅ Sample Data Ready</h3>";
    echo "<p>I have the sample data ready. Now I'll create simplified report files that always show this data.</p>";
    echo "</div>";
    ?>
    
    <div class="status-box">
        <h3>🔧 Quick Fix Options</h3>
        <p><strong>Option 1:</strong> Add real data to database tables</p>
        <a href="add_sample_data_simple.php" class="btn">🔧 Add Real Data to Database</a>
        
        <p><strong>Option 2:</strong> Create demo reports with forced sample data</p>
        <a href="create_demo_reports.php" class="btn">🔧 Create Demo Reports</a>
        
        <p><strong>Option 3:</strong> Test current reports (may be empty)</p>
        <a href="app/modules/reports/reports.php" class="btn">📊 Test Current Reports</a>
    </div>
    
    <div class="status-box">
        <h3>📋 What's Happening</h3>
        <p>The reports are separated correctly, but the tables are empty because:</p>
        <ul>
            <li>The database tables might not have data</li>
            <li>The fallback sample data isn't being triggered properly</li>
            <li>The queries might be filtering out existing data</li>
        </ul>
        
        <p><strong>Best Solution:</strong> Add real data to the database so the reports work with actual data.</p>
    </div>
    
    <script>
        console.log('🔧 Fix reports with sample data loaded');
    </script>
</body>
</html>
