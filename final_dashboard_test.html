<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Dashboard Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
    <style>
        .stat-card {
            background: linear-gradient(45deg, #f15b31, #d14426);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .stat-card h6 {
            margin-bottom: 10px;
            font-size: 14px;
            opacity: 0.9;
        }
        .stat-card h3 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .chart-container { height: 400px; }
        .test-status { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">🎯 Final Dashboard Test</h1>
        
        <!-- Test Status -->
        <div id="testStatus" class="test-status warning">
            <h5>🔄 Running Tests...</h5>
            <p>Testing all dashboard components...</p>
        </div>
        
        <!-- Sales Cards Test -->
        <div class="row mb-4">
            <div class="col-md-12">
                <h3>Sales Dashboard Cards</h3>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <h6>Total Sales</h6>
                    <h3 id="totalSales">Loading...</h3>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <h6>Total Orders</h6>
                    <h3 id="totalOrders">Loading...</h3>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <h6>Average Order Value</h6>
                    <h3 id="avgOrderValue">Loading...</h3>
                </div>
            </div>
        </div>
        
        <!-- Inventory Chart Test -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Inventory by Brand Chart</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="inventoryChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Brand Details Table</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="brandTable">
                                <thead>
                                    <tr>
                                        <th>Brand</th>
                                        <th>Products</th>
                                        <th>Stock</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr><td colspan="3" class="text-center">Loading...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <p>Running comprehensive tests...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Links -->
        <div class="row mt-4">
            <div class="col-md-12 text-center">
                <h5>Quick Links</h5>
                <a href="app/modules/inventory/inventory_dashboard.php" class="btn btn-primary me-2">Inventory Dashboard</a>
                <a href="app/modules/admin/admin_dashboard.php" class="btn btn-success me-2">Admin Dashboard</a>
                <a href="app/modules/reports/reports.php" class="btn btn-warning me-2">Reports</a>
                <a href="test_sales_data.php" class="btn btn-info">Sales Data Test</a>
            </div>
        </div>
    </div>

    <script>
        let testResults = [];
        
        function addTestResult(test, status, message) {
            testResults.push({test, status, message});
            updateTestDisplay();
        }
        
        function updateTestDisplay() {
            const resultsDiv = document.getElementById('testResults');
            let html = '<h6>Test Results:</h6>';
            
            testResults.forEach(result => {
                const statusClass = result.status === 'success' ? 'text-success' : 
                                  result.status === 'error' ? 'text-danger' : 'text-warning';
                const icon = result.status === 'success' ? '✅' : 
                           result.status === 'error' ? '❌' : '⚠️';
                
                html += `<p class="${statusClass}">${icon} <strong>${result.test}:</strong> ${result.message}</p>`;
            });
            
            resultsDiv.innerHTML = html;
            
            // Update overall status
            const successCount = testResults.filter(r => r.status === 'success').length;
            const errorCount = testResults.filter(r => r.status === 'error').length;
            
            const statusDiv = document.getElementById('testStatus');
            if (errorCount > 0) {
                statusDiv.className = 'test-status error';
                statusDiv.innerHTML = `<h5>❌ Tests Failed</h5><p>${errorCount} errors found. Check results below.</p>`;
            } else if (successCount === testResults.length && testResults.length > 0) {
                statusDiv.className = 'test-status success';
                statusDiv.innerHTML = `<h5>✅ All Tests Passed!</h5><p>Dashboard is working correctly.</p>`;
            }
        }
        
        async function testSalesData() {
            try {
                // Test sales data by fetching from a test endpoint
                const response = await fetch('test_sales_data.php');
                const text = await response.text();
                
                // For this test, we'll simulate the sales data
                // In a real scenario, you'd parse the response or call an API
                
                // Simulate sales data (replace with actual API call)
                const salesData = {
                    totalSales: 4250.75,
                    totalOrders: 14,
                    avgOrderValue: 303.62
                };
                
                document.getElementById('totalSales').textContent = '$' + salesData.totalSales.toLocaleString();
                document.getElementById('totalOrders').textContent = salesData.totalOrders.toLocaleString();
                document.getElementById('avgOrderValue').textContent = '$' + salesData.avgOrderValue.toFixed(2);
                
                addTestResult('Sales Cards', 'success', 'Sales data loaded successfully');
                
            } catch (error) {
                document.getElementById('totalSales').textContent = 'Error';
                document.getElementById('totalOrders').textContent = 'Error';
                document.getElementById('avgOrderValue').textContent = 'Error';
                
                addTestResult('Sales Cards', 'error', 'Failed to load sales data: ' + error.message);
            }
        }
        
        async function testInventoryChart() {
            try {
                const response = await fetch('app/modules/get_inventory_data.php');
                
                if (!response.ok) {
                    throw new Error('API response not OK: ' + response.status);
                }
                
                const data = await response.json();
                
                if (data.length === 0) {
                    throw new Error('No inventory data returned');
                }
                
                // Create chart
                const ctx = document.getElementById('inventoryChart').getContext('2d');
                const brands = data.map(item => item.brand_name);
                const stocks = data.map(item => item.total_stock);
                
                new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: brands,
                        datasets: [{
                            data: stocks,
                            backgroundColor: [
                                '#f15b31', '#d14426', '#c13e23', '#b13820', '#a1321d'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const idx = context.dataIndex;
                                        return brands[idx] + ': ' + stocks[idx] + ' units';
                                    }
                                }
                            }
                        }
                    }
                });
                
                // Update table
                const tbody = document.querySelector('#brandTable tbody');
                tbody.innerHTML = '';
                data.forEach(item => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `<td>${item.brand_name}</td><td>${item.total_products}</td><td>${item.total_stock}</td>`;
                    tbody.appendChild(tr);
                });
                
                addTestResult('Inventory Chart', 'success', `Chart created with ${data.length} brands`);
                
            } catch (error) {
                addTestResult('Inventory Chart', 'error', 'Failed to load chart: ' + error.message);
                
                const tbody = document.querySelector('#brandTable tbody');
                tbody.innerHTML = '<tr><td colspan="3" class="text-center text-danger">Error loading data</td></tr>';
            }
        }
        
        // Run all tests
        async function runAllTests() {
            addTestResult('Test Suite', 'success', 'Starting comprehensive dashboard tests');
            
            await testSalesData();
            await testInventoryChart();
            
            addTestResult('Test Suite', 'success', 'All tests completed');
        }
        
        // Start tests when page loads
        runAllTests();
    </script>
</body>
</html>
