<!DOCTYPE html>
<html>
<head>
    <title>Test Admin Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-box { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        .error { background: #ffebee; border-color: #f44336; }
        .warning { background: #fff3e0; border-color: #ff9800; }
        .btn { background: #f15b31; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
        .stats-preview { display: flex; gap: 15px; flex-wrap: wrap; margin: 15px 0; }
        .stat-card { background: #D14118; color: white; padding: 15px; border-radius: 8px; text-align: center; min-width: 150px; }
        .stat-card h5 { margin: 0 0 10px 0; font-size: 14px; }
        .stat-card h2 { margin: 0; font-size: 24px; }
    </style>
</head>
<body>
    <h1>🔧 Test Admin Dashboard</h1>
    
    <?php
    require_once 'app/config/config.php';
    
    echo "<div class='test-box'>";
    echo "<h3>1. Database Connection</h3>";
    try {
        $stmt = $conn->prepare("SELECT 1");
        $stmt->execute();
        echo "<p class='success'>✅ Database connected successfully</p>";
    } catch (Exception $e) {
        echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    echo "<div class='test-box'>";
    echo "<h3>2. Module Statistics Test</h3>";
    
    // Test each module stat
    $modules = ['users', 'products', 'inventory'];
    $moduleStats = [];
    
    foreach ($modules as $module) {
        try {
            switch ($module) {
                case 'users':
                    $query = "SELECT COUNT(*) as total FROM users";
                    $stmt = $conn->prepare($query);
                    $stmt->execute();
                    $result = $stmt->fetch(PDO::FETCH_ASSOC);
                    $moduleStats[$module]['total_users'] = $result['total'] ?? 0;
                    echo "<p>👥 Users: " . $moduleStats[$module]['total_users'] . "</p>";
                    break;
                case 'products':
                    $query = "SELECT COUNT(*) as total FROM products";
                    $stmt = $conn->prepare($query);
                    $stmt->execute();
                    $result = $stmt->fetch(PDO::FETCH_ASSOC);
                    $moduleStats[$module]['total_products'] = $result['total'] ?? 0;
                    echo "<p>📦 Products: " . $moduleStats[$module]['total_products'] . "</p>";
                    break;
                case 'inventory':
                    $query = "SELECT COUNT(*) as total FROM inventory WHERE status = 'approved'";
                    $stmt = $conn->prepare($query);
                    $stmt->execute();
                    $result = $stmt->fetch(PDO::FETCH_ASSOC);
                    $moduleStats[$module]['total_items'] = $result['total'] ?? 0;
                    echo "<p>📋 Inventory Items: " . $moduleStats[$module]['total_items'] . "</p>";
                    break;
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ Error getting $module stats: " . $e->getMessage() . "</p>";
            $moduleStats[$module] = ['total_users' => 0, 'total_products' => 0, 'total_items' => 0];
        }
    }
    echo "</div>";
    
    echo "<div class='test-box'>";
    echo "<h3>3. Sales Data Test</h3>";
    try {
        $sales_sql = "SELECT
            COUNT(*) as total_orders,
            SUM(total_amount) as total_sales,
            AVG(total_amount) as average_order_value
          FROM procurement_orders
          WHERE status = 'completed'";

        $sales_stmt = $conn->prepare($sales_sql);
        $sales_stmt->execute();
        $sales_result = $sales_stmt->fetch(PDO::FETCH_ASSOC);
        
        $total_sales = $sales_result['total_sales'] ?? 0;
        $total_orders = $sales_result['total_orders'] ?? 0;
        $avg_order_value = $sales_result['average_order_value'] ?? 0;
        
        echo "<p>📊 Total Orders: " . number_format($total_orders) . "</p>";
        echo "<p>💰 Total Sales: $" . number_format($total_sales, 2) . "</p>";
        echo "<p>📈 Average Order Value: $" . number_format($avg_order_value, 2) . "</p>";
        
        if ($total_orders > 0) {
            echo "<p class='success'>✅ Sales data is working!</p>";
        } else {
            echo "<p class='warning'>⚠️ No sales data found. Need to add sample data.</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Sales query error: " . $e->getMessage() . "</p>";
        $total_sales = 0;
        $total_orders = 0;
        $avg_order_value = 0;
    }
    echo "</div>";
    
    echo "<div class='test-box'>";
    echo "<h3>4. Admin Dashboard Cards Preview</h3>";
    echo "<p>This is how your admin dashboard cards will look:</p>";
    
    echo "<div class='stats-preview'>";
    echo "<div class='stat-card'>";
    echo "<h5>Total Users</h5>";
    echo "<h2>" . ($moduleStats['users']['total_users'] ?? 0) . "</h2>";
    echo "</div>";
    
    echo "<div class='stat-card'>";
    echo "<h5>Total Products</h5>";
    echo "<h2>" . ($moduleStats['products']['total_products'] ?? 0) . "</h2>";
    echo "</div>";
    
    echo "<div class='stat-card'>";
    echo "<h5>Inventory Items</h5>";
    echo "<h2>" . ($moduleStats['inventory']['total_items'] ?? 0) . "</h2>";
    echo "</div>";
    
    echo "<div class='stat-card'>";
    echo "<h5>Total Orders</h5>";
    echo "<h2>" . number_format($total_orders) . "</h2>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='stats-preview'>";
    echo "<div class='stat-card'>";
    echo "<h5>Total Sales</h5>";
    echo "<h2>$" . number_format($total_sales, 2) . "</h2>";
    echo "</div>";
    
    echo "<div class='stat-card'>";
    echo "<h5>Average Order Value</h5>";
    echo "<h2>$" . ($total_orders > 0 ? number_format($avg_order_value, 2) : '0.00') . "</h2>";
    echo "</div>";
    
    echo "<div class='stat-card'>";
    echo "<h5>System Status</h5>";
    echo "<h2>" . (($total_sales > 0 && $moduleStats['inventory']['total_items'] > 0) ? '✅ Active' : '⚠️ Setup') . "</h2>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='test-box'>";
    echo "<h3>5. Dashboard Status</h3>";
    
    $all_good = true;
    $issues = [];
    
    if ($moduleStats['users']['total_users'] == 0) {
        $issues[] = "No users found";
        $all_good = false;
    }
    
    if ($moduleStats['inventory']['total_items'] == 0) {
        $issues[] = "No inventory items found";
        $all_good = false;
    }
    
    if ($total_orders == 0) {
        $issues[] = "No completed orders found";
        $all_good = false;
    }
    
    if ($all_good) {
        echo "<p class='success'>🎉 Admin dashboard is ready! All data is available.</p>";
    } else {
        echo "<p class='warning'>⚠️ Some data is missing:</p>";
        foreach ($issues as $issue) {
            echo "<p>• $issue</p>";
        }
        echo "<p>Run the sample data script to fix these issues.</p>";
    }
    echo "</div>";
    ?>
    
    <div class="test-box">
        <h3>6. Action Links</h3>
        
        <?php if (!$all_good): ?>
            <a href="add_sample_data_simple.php" class="btn">🔧 Add Sample Data</a>
        <?php endif; ?>
        
        <a href="app/modules/admin/admin_dashboard.php" class="btn">📊 Test Admin Dashboard</a>
        <a href="app/modules/inventory/inventory_dashboard.php" class="btn">📦 Test Inventory Dashboard</a>
        <a href="test_separated_dashboard.php" class="btn">🔍 Test Separated Dashboard</a>
    </div>
    
    <div class="test-box">
        <h3>7. What I Fixed in Admin Dashboard</h3>
        <p>✅ <strong>Added Sales Cards</strong> - Now shows Total Sales, Average Order Value, System Status</p>
        <p>✅ <strong>Improved Layout</strong> - Changed from 3 cards to 4 cards in first row, added second row</p>
        <p>✅ <strong>Better Error Handling</strong> - Added proper error logging for all queries</p>
        <p>✅ <strong>Direct Data Fetching</strong> - No more includes, all data fetched directly</p>
        <p>✅ <strong>System Status Indicator</strong> - Shows if system is properly set up</p>
        
        <p><strong>Admin dashboard now has 7 cards total:</strong></p>
        <p>• Row 1: Total Users, Total Products, Inventory Items, Total Orders</p>
        <p>• Row 2: Total Sales, Average Order Value, System Status</p>
    </div>
    
    <script>
        console.log('🔍 Admin dashboard test page loaded');
    </script>
</body>
</html>
