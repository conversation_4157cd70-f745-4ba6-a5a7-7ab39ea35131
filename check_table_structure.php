<?php
require_once 'app/config/config.php';

echo "<h2>🔍 Checking Database Table Structures</h2>";

try {
    // Check what tables exist
    echo "<h3>📋 Available Tables:</h3>";
    $tables = $conn->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li><strong>$table</strong></li>";
    }
    echo "</ul>";
    
    echo "<hr>";
    
    // Check users table structure
    echo "<h3>👤 Users Table Structure:</h3>";
    try {
        $users_structure = $conn->query("DESCRIBE users")->fetchAll(PDO::FETCH_ASSOC);
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($users_structure as $field) {
            echo "<tr>";
            echo "<td>" . $field['Field'] . "</td>";
            echo "<td>" . $field['Type'] . "</td>";
            echo "<td>" . $field['Null'] . "</td>";
            echo "<td>" . $field['Key'] . "</td>";
            echo "<td>" . $field['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Sample users data
        $users_count = $conn->query("SELECT COUNT(*) FROM users")->fetchColumn();
        echo "<p><strong>Total users:</strong> $users_count</p>";
        
        if ($users_count > 0) {
            $sample_users = $conn->query("SELECT * FROM users LIMIT 3")->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Sample data:</strong></p>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            if (!empty($sample_users)) {
                echo "<tr>";
                foreach (array_keys($sample_users[0]) as $column) {
                    echo "<th>$column</th>";
                }
                echo "</tr>";
                foreach ($sample_users as $user) {
                    echo "<tr>";
                    foreach ($user as $value) {
                        echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                    }
                    echo "</tr>";
                }
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Users table error: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
    
    // Check departments table structure
    echo "<h3>🏢 Departments Table Structure:</h3>";
    try {
        $dept_structure = $conn->query("DESCRIBE departments")->fetchAll(PDO::FETCH_ASSOC);
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($dept_structure as $field) {
            echo "<tr>";
            echo "<td>" . $field['Field'] . "</td>";
            echo "<td>" . $field['Type'] . "</td>";
            echo "<td>" . $field['Null'] . "</td>";
            echo "<td>" . $field['Key'] . "</td>";
            echo "<td>" . $field['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        $dept_count = $conn->query("SELECT COUNT(*) FROM departments")->fetchColumn();
        echo "<p><strong>Total departments:</strong> $dept_count</p>";
        
        if ($dept_count > 0) {
            $sample_depts = $conn->query("SELECT * FROM departments LIMIT 5")->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Sample data:</strong></p>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            if (!empty($sample_depts)) {
                echo "<tr>";
                foreach (array_keys($sample_depts[0]) as $column) {
                    echo "<th>$column</th>";
                }
                echo "</tr>";
                foreach ($sample_depts as $dept) {
                    echo "<tr>";
                    foreach ($dept as $value) {
                        echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                    }
                    echo "</tr>";
                }
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Departments table error: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
    
    // Check roles table structure
    echo "<h3>🔐 Roles Table Structure:</h3>";
    try {
        $roles_structure = $conn->query("DESCRIBE roles")->fetchAll(PDO::FETCH_ASSOC);
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($roles_structure as $field) {
            echo "<tr>";
            echo "<td>" . $field['Field'] . "</td>";
            echo "<td>" . $field['Type'] . "</td>";
            echo "<td>" . $field['Null'] . "</td>";
            echo "<td>" . $field['Key'] . "</td>";
            echo "<td>" . $field['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        $roles_count = $conn->query("SELECT COUNT(*) FROM roles")->fetchColumn();
        echo "<p><strong>Total roles:</strong> $roles_count</p>";
        
        if ($roles_count > 0) {
            $sample_roles = $conn->query("SELECT * FROM roles LIMIT 5")->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Sample data:</strong></p>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            if (!empty($sample_roles)) {
                echo "<tr>";
                foreach (array_keys($sample_roles[0]) as $column) {
                    echo "<th>$column</th>";
                }
                echo "</tr>";
                foreach ($sample_roles as $role) {
                    echo "<tr>";
                    foreach ($role as $value) {
                        echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                    }
                    echo "</tr>";
                }
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Roles table error: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
    
    // Check drivers table structure
    echo "<h3>🚛 Drivers Table Structure:</h3>";
    try {
        $drivers_structure = $conn->query("DESCRIBE drivers")->fetchAll(PDO::FETCH_ASSOC);
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($drivers_structure as $field) {
            echo "<tr>";
            echo "<td>" . $field['Field'] . "</td>";
            echo "<td>" . $field['Type'] . "</td>";
            echo "<td>" . $field['Null'] . "</td>";
            echo "<td>" . $field['Key'] . "</td>";
            echo "<td>" . $field['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        $drivers_count = $conn->query("SELECT COUNT(*) FROM drivers")->fetchColumn();
        echo "<p><strong>Total drivers:</strong> $drivers_count</p>";
        
        if ($drivers_count > 0) {
            $sample_drivers = $conn->query("SELECT * FROM drivers LIMIT 3")->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Sample data:</strong></p>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            if (!empty($sample_drivers)) {
                echo "<tr>";
                foreach (array_keys($sample_drivers[0]) as $column) {
                    echo "<th>$column</th>";
                }
                echo "</tr>";
                foreach ($sample_drivers as $driver) {
                    echo "<tr>";
                    foreach ($driver as $value) {
                        echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                    }
                    echo "</tr>";
                }
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Drivers table error: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
    
    // Check payroll table structure
    echo "<h3>💰 Payroll Table Structure:</h3>";
    try {
        $payroll_structure = $conn->query("DESCRIBE payroll")->fetchAll(PDO::FETCH_ASSOC);
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($payroll_structure as $field) {
            echo "<tr>";
            echo "<td>" . $field['Field'] . "</td>";
            echo "<td>" . $field['Type'] . "</td>";
            echo "<td>" . $field['Null'] . "</td>";
            echo "<td>" . $field['Key'] . "</td>";
            echo "<td>" . $field['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        $payroll_count = $conn->query("SELECT COUNT(*) FROM payroll")->fetchColumn();
        echo "<p><strong>Total payroll records:</strong> $payroll_count</p>";
        
        if ($payroll_count > 0) {
            $sample_payroll = $conn->query("SELECT * FROM payroll LIMIT 3")->fetchAll(PDO::FETCH_ASSOC);
            echo "<p><strong>Sample data:</strong></p>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            if (!empty($sample_payroll)) {
                echo "<tr>";
                foreach (array_keys($sample_payroll[0]) as $column) {
                    echo "<th>$column</th>";
                }
                echo "</tr>";
                foreach ($sample_payroll as $record) {
                    echo "<tr>";
                    foreach ($record as $value) {
                        echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                    }
                    echo "</tr>";
                }
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Payroll table error: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "❌ Database connection error: " . $e->getMessage();
}
?>
