<?php
session_start();
require_once "../../config/config.php";

if (!isset($_SESSION["user_id"])) {
    header("Location: ../auth/login.php");
    exit();
}

// Force sample data for demonstration
$dept_expenses_result = [
    ["department" => "Logistics", "order_count" => 18, "total_spent" => 15680.25],
    ["department" => "Finance", "order_count" => 15, "total_spent" => 12450.75],
    ["department" => "Inventory", "order_count" => 14, "total_spent" => 10340.80],
    ["department" => "Procurement", "order_count" => 12, "total_spent" => 8920.50],
    ["department" => "Sales", "order_count" => 10, "total_spent" => 7890.45]
];

$total_expenses = array_sum(array_column($dept_expenses_result, "total_spent"));
$total_orders = array_sum(array_column($dept_expenses_result, "order_count"));
$pending_orders = 8;
$pending_amount = 5420.30;
$inventory_value = 8644.15;
$inventory_items = 12;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finance Report (Demo)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #faf2e9; }
        .page-title { 
            background-color: #f15b31; 
            color: white; 
            padding: 15px; 
            border-radius: 5px; 
            margin-bottom: 20px; 
            text-align: center;
        }
        .stat-card {
            background: linear-gradient(45deg, #f15b31, #d14426);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .stat-card h6 { margin-bottom: 10px; font-size: 14px; opacity: 0.9; }
        .stat-card h3 { margin: 0; font-size: 28px; font-weight: bold; }
        .table { background-color: white; color: black; }
        .table th { background-color: #f15b31; color: white; border-color: #d14426; }
        .table td { border-color: #e9ecef; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="page-title">💰 Finance Report (Demo with Sample Data)</h1>
        
        <div class="row mb-4">
            <div class="col-12 text-end">
                <a href="reports_demo.php" class="btn" style="background-color: #f15b31; color: white;">Back to Reports</a>
            </div>
        </div>
        
        <!-- Financial Overview Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card">
                    <h6>Total Expenses</h6>
                    <h3>$<?php echo number_format($total_expenses, 2); ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <h6>Pending Budget</h6>
                    <h3>$<?php echo number_format($pending_amount, 2); ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <h6>Inventory Value</h6>
                    <h3>$<?php echo number_format($inventory_value, 2); ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <h6>Total Orders</h6>
                    <h3><?php echo number_format($total_orders); ?></h3>
                </div>
            </div>
        </div>
        
        <!-- Department Expenses Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header" style="background-color: #f15b31; color: white;">
                        <h5>Expenses by Department</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-bordered mb-0">
                                <thead>
                                    <tr>
                                        <th>Department</th>
                                        <th>Orders</th>
                                        <th>Total Spent</th>
                                        <th>Avg per Order</th>
                                        <th>% of Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($dept_expenses_result as $dept): ?>
                                        <?php 
                                        $avg_per_order = $dept["order_count"] > 0 ? $dept["total_spent"] / $dept["order_count"] : 0;
                                        $percentage = $total_expenses > 0 ? ($dept["total_spent"] / $total_expenses) * 100 : 0;
                                        ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($dept["department"]); ?></td>
                                            <td><?php echo number_format($dept["order_count"]); ?></td>
                                            <td>$<?php echo number_format($dept["total_spent"], 2); ?></td>
                                            <td>$<?php echo number_format($avg_per_order, 2); ?></td>
                                            <td><?php echo number_format($percentage, 1); ?>%</td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Financial Summary -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header" style="background-color: #f15b31; color: white;">
                        <h5>Financial Summary</h5>
                    </div>
                    <div class="card-body" style="background-color: #f15b31; color: white;">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Period: <?php echo date("F Y"); ?></h6>
                                <p><strong>Total Completed Orders:</strong> <?php echo number_format($total_orders); ?></p>
                                <p><strong>Total Expenses:</strong> $<?php echo number_format($total_expenses, 2); ?></p>
                                <p><strong>Average Order Value:</strong> $<?php echo $total_orders > 0 ? number_format($total_expenses / $total_orders, 2) : "0.00"; ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Pending Orders:</strong> <?php echo number_format($pending_orders); ?></p>
                                <p><strong>Pending Budget:</strong> $<?php echo number_format($pending_amount, 2); ?></p>
                                <p><strong>Current Inventory Value:</strong> $<?php echo number_format($inventory_value, 2); ?></p>
                                <p><strong>Inventory Items:</strong> <?php echo number_format($inventory_items); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <button onclick="window.print()" class="btn" style="background-color: #f15b31; color: white;">
                🖨️ Print Report
            </button>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
