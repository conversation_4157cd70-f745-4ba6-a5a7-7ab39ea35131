<?php
session_start();
require_once "../../config/config.php";

// Check if user is logged in
if (!isset($_SESSION["user_id"])) {
    header("Location: ../auth/login.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports Dashboard (Demo)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #faf2e9; }
        .page-title {
            background-color: #f15b31;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        .report-card {
            background: linear-gradient(45deg, #f15b31, #d14426);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease;
            text-decoration: none;
            display: block;
        }
        .report-card:hover {
            transform: translateY(-5px);
            color: white;
            text-decoration: none;
        }
        .report-card h4 { margin-bottom: 15px; font-weight: bold; }
        .report-card p { margin-bottom: 10px; opacity: 0.9; }
        .report-card .icon { font-size: 48px; margin-bottom: 15px; }
        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #f15b31;
        }
        .stats-card h6 { color: #f15b31; margin-bottom: 10px; }
        .stats-card h3 { color: #333; margin: 0; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="page-title">📊 Reports Dashboard (Demo with Sample Data)</h1>

        <div class="row mb-4">
            <div class="col-12 text-end">
                <a href="../admin/admin_dashboard.php" class="btn" style="background-color: #f15b31; color: white;">Back to Dashboard</a>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stats-card">
                    <h6>Total Sales Orders</h6>
                    <h3>5</h3>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <h6>Inventory Items</h6>
                    <h3>6</h3>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <h6>Monthly Expenses</h6>
                    <h3>$55,282.75</h3>
                </div>
            </div>
        </div>

        <!-- Report Cards -->
        <div class="row">
            <div class="col-md-4">
                <a href="sales_report_demo.php" class="report-card">
                    <div class="icon">📈</div>
                    <h4>Sales Report</h4>
                    <p>View detailed sales analytics</p>
                    <p>Orders, revenue, and trends</p>
                    <small>Demo with sample data</small>
                </a>
            </div>
            <div class="col-md-4">
                <a href="inventory_report_demo.php" class="report-card">
                    <div class="icon">📦</div>
                    <h4>Inventory Report</h4>
                    <p>Track inventory levels and values</p>
                    <p>Stock levels, brands, and products</p>
                    <small>Demo with sample data</small>
                </a>
            </div>
            <div class="col-md-4">
                <a href="finance_report_demo.php" class="report-card">
                    <div class="icon">💰</div>
                    <h4>Finance Report</h4>
                    <p>Monitor expenses and budgets</p>
                    <p>Department spending and costs</p>
                    <small>Demo with sample data</small>
                </a>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h5>📋 Demo Reports Information</h5>
                    <p>These are demo reports with guaranteed sample data to show you how the separated reports work.</p>
                    <p>The original reports are still available but may show empty tables if there's no data in the database.</p>
                    <a href="reports.php" class="btn btn-outline-primary">View Original Reports</a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>