<?php
require_once '../config/config.php';
header('Content-Type: application/json');

try {
    // Try to get low stock data from inventory table first
    $sql = "SELECT prod_name as name, brand_name, stocks as stock FROM inventory WHERE status = 'approved' AND stocks < 50 ORDER BY stocks ASC LIMIT 10";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $data = [];
    if (count($result) > 0) {
        foreach ($result as $row) {
            $data[] = [
                'name' => $row['name'],
                'brand_name' => $row['brand_name'],
                'stock' => (int)$row['stock']
            ];
        }
    } else {
        // Fallback to products table if inventory is empty
        $sql = "SELECT name, 'Unknown' as brand_name, stock_quantity as stock FROM products WHERE stock_quantity < 10 ORDER BY stock_quantity ASC LIMIT 10";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($result) > 0) {
            foreach ($result as $row) {
                $data[] = [
                    'name' => $row['name'],
                    'brand_name' => $row['brand_name'],
                    'stock' => (int)$row['stock']
                ];
            }
        }
    }
    
    echo json_encode($data);
} catch (Exception $e) {
    echo json_encode([]);
}
?>
