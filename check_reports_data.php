<!DOCTYPE html>
<html>
<head>
    <title>Check Reports Data</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-box { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        .error { background: #ffebee; border-color: #f44336; }
        .warning { background: #fff3e0; border-color: #ff9800; }
        .btn { background: #f15b31; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f15b31; color: white; }
    </style>
</head>
<body>
    <h1>🔍 Check Reports Data</h1>
    
    <?php
    require_once 'app/config/config.php';
    
    echo "<div class='test-box'>";
    echo "<h3>1. Sales Data Check</h3>";
    try {
        $sales_sql = "SELECT 
            order_number, 
            total_amount, 
            status, 
            created_at 
            FROM procurement_orders 
            ORDER BY created_at DESC 
            LIMIT 10";
        
        $stmt = $conn->prepare($sales_sql);
        $stmt->execute();
        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($orders) > 0) {
            echo "<p class='success'>✅ Found " . count($orders) . " orders</p>";
            echo "<table>";
            echo "<tr><th>Order Number</th><th>Amount</th><th>Status</th><th>Date</th></tr>";
            foreach ($orders as $order) {
                echo "<tr>";
                echo "<td>" . $order['order_number'] . "</td>";
                echo "<td>$" . number_format($order['total_amount'], 2) . "</td>";
                echo "<td>" . $order['status'] . "</td>";
                echo "<td>" . $order['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>❌ No orders found in procurement_orders table</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    echo "<div class='test-box'>";
    echo "<h3>2. Inventory Data Check</h3>";
    try {
        $inventory_sql = "SELECT 
            prod_name, 
            brand_name, 
            stocks, 
            price, 
            status 
            FROM inventory 
            ORDER BY brand_name, prod_name 
            LIMIT 10";
        
        $stmt = $conn->prepare($inventory_sql);
        $stmt->execute();
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($products) > 0) {
            echo "<p class='success'>✅ Found " . count($products) . " products</p>";
            echo "<table>";
            echo "<tr><th>Product</th><th>Brand</th><th>Stock</th><th>Price</th><th>Status</th></tr>";
            foreach ($products as $product) {
                echo "<tr>";
                echo "<td>" . $product['prod_name'] . "</td>";
                echo "<td>" . $product['brand_name'] . "</td>";
                echo "<td>" . $product['stocks'] . "</td>";
                echo "<td>$" . number_format($product['price'], 2) . "</td>";
                echo "<td>" . $product['status'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>❌ No products found in inventory table</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    echo "<div class='test-box'>";
    echo "<h3>3. Users and Departments Check</h3>";
    try {
        $users_sql = "SELECT COUNT(*) as user_count FROM users";
        $stmt = $conn->prepare($users_sql);
        $stmt->execute();
        $user_result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $dept_sql = "SELECT COUNT(*) as dept_count FROM departments";
        $stmt = $conn->prepare($dept_sql);
        $stmt->execute();
        $dept_result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<p>👥 Users: " . $user_result['user_count'] . "</p>";
        echo "<p>🏢 Departments: " . $dept_result['dept_count'] . "</p>";
        
        if ($user_result['user_count'] > 0 && $dept_result['dept_count'] > 0) {
            echo "<p class='success'>✅ Users and departments exist</p>";
        } else {
            echo "<p class='warning'>⚠️ Missing users or departments</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    echo "<div class='test-box'>";
    echo "<h3>4. Quick Fix</h3>";
    
    if (count($orders) == 0 || count($products) == 0) {
        echo "<p class='warning'>⚠️ Reports are empty because there's no data!</p>";
        echo "<p><strong>Solution:</strong> Add sample data to see the reports working.</p>";
        echo "<a href='add_sample_data_simple.php' class='btn'>🔧 Add Sample Data</a>";
    } else {
        echo "<p class='success'>✅ Data exists! Reports should show content.</p>";
        echo "<p>If reports are still empty, there might be a query issue.</p>";
    }
    echo "</div>";
    
    echo "<div class='test-box'>";
    echo "<h3>5. Test Reports Again</h3>";
    echo "<a href='app/modules/reports/reports.php' class='btn'>📊 Main Reports</a>";
    echo "<a href='app/modules/reports/sales_report.php' class='btn'>📈 Sales Report</a>";
    echo "<a href='app/modules/reports/inventory_report.php' class='btn'>📦 Inventory Report</a>";
    echo "<a href='app/modules/reports/finance_report.php' class='btn'>💰 Finance Report</a>";
    echo "</div>";
    
    echo "<div class='test-box'>";
    echo "<h3>6. What's Happening</h3>";
    echo "<p>Yung reports ay naka-separate na, pero kung walang data sa database, empty talaga sila.</p>";
    echo "<p>Hindi nawala yung laman - kailangan lang ng data para makita mo yung content.</p>";
    echo "<p>Once may data na, makikita mo na yung:</p>";
    echo "<ul>";
    echo "<li>📈 Sales transactions table</li>";
    echo "<li>📦 Inventory products table</li>";
    echo "<li>💰 Department expenses table</li>";
    echo "</ul>";
    echo "</div>";
    ?>
    
    <script>
        console.log('🔍 Checking reports data...');
    </script>
</body>
</html>
