<?php
require_once 'app/config/config.php';

try {
    // Create employee_performance table
    $sql = "CREATE TABLE IF NOT EXISTS employee_performance (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        department_id INT,
        role_id INT,
        evaluation_date DATE NOT NULL,
        performance_score DECIMAL(3,2) NOT NULL COMMENT 'Score from 1.00 to 5.00',
        goals_achieved INT DEFAULT 0 COMMENT 'Number of goals achieved',
        total_goals INT DEFAULT 0 COMMENT 'Total number of goals set',
        attendance_rate DECIMAL(5,2) DEFAULT 100.00 COMMENT 'Attendance percentage',
        supervisor_feedback TEXT,
        employee_comments TEXT,
        improvement_areas TEXT,
        strengths TEXT,
        next_review_date DATE,
        status ENUM('draft', 'completed', 'approved', 'needs_improvement') DEFAULT 'draft',
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREI<PERSON><PERSON> KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
    )";
    
    $conn->exec($sql);
    echo "✅ Employee Performance table created successfully!<br>";
    
    // Insert sample data
    $sample_data = [
        [1, 1, 1, '2024-01-15', 4.5, 8, 10, 95.50, 'Excellent performance this quarter. Shows great leadership skills.', 'I feel confident in my role and enjoy the challenges.', 'Time management could be improved', 'Strong analytical skills, good team player', '2024-04-15', 'approved', 1],
        [2, 2, 2, '2024-01-20', 3.8, 6, 8, 88.75, 'Good performance with room for improvement in communication.', 'Looking forward to taking on more responsibilities.', 'Communication skills, presentation skills', 'Detail-oriented, reliable', '2024-04-20', 'completed', 1],
        [3, 3, 3, '2024-01-25', 4.2, 7, 9, 92.30, 'Consistently meets expectations and shows initiative.', 'Happy with current projects and team collaboration.', 'Technical skills development', 'Problem-solving, creativity', '2024-04-25', 'approved', 1],
        [4, 4, 1, '2024-02-01', 3.5, 5, 10, 85.60, 'Performance is satisfactory but needs more focus on deadlines.', 'Need more training in new systems.', 'Time management, prioritization', 'Enthusiasm, willingness to learn', '2024-05-01', 'needs_improvement', 1],
        [5, 5, 2, '2024-02-05', 4.8, 9, 10, 98.20, 'Outstanding performance. Exceeds all expectations consistently.', 'Very satisfied with my growth and opportunities.', 'None identified at this time', 'Leadership, innovation, mentoring', '2024-05-05', 'approved', 1],
        [6, 6, 3, '2024-02-10', 4.0, 7, 8, 90.45, 'Solid performance with good technical skills.', 'Enjoying the work environment and team dynamics.', 'Cross-functional collaboration', 'Technical expertise, attention to detail', '2024-05-10', 'completed', 1]
    ];
    
    $insert_sql = "INSERT INTO employee_performance 
                   (user_id, department_id, role_id, evaluation_date, performance_score, goals_achieved, total_goals, 
                    attendance_rate, supervisor_feedback, employee_comments, improvement_areas, strengths, 
                    next_review_date, status, created_by) 
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($insert_sql);
    
    foreach ($sample_data as $data) {
        $stmt->execute($data);
    }
    
    echo "✅ Sample employee performance data inserted successfully!<br>";
    
    // Show the created table structure
    echo "<h3>Employee Performance Table Structure:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 20px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Description</th></tr>";
    echo "<tr><td>id</td><td>INT AUTO_INCREMENT</td><td>Primary key</td></tr>";
    echo "<tr><td>user_id</td><td>INT</td><td>Links to users table</td></tr>";
    echo "<tr><td>department_id</td><td>INT</td><td>Links to departments table</td></tr>";
    echo "<tr><td>role_id</td><td>INT</td><td>Links to roles table</td></tr>";
    echo "<tr><td>evaluation_date</td><td>DATE</td><td>Date of performance evaluation</td></tr>";
    echo "<tr><td>performance_score</td><td>DECIMAL(3,2)</td><td>Score from 1.00 to 5.00</td></tr>";
    echo "<tr><td>goals_achieved</td><td>INT</td><td>Number of goals achieved</td></tr>";
    echo "<tr><td>total_goals</td><td>INT</td><td>Total number of goals set</td></tr>";
    echo "<tr><td>attendance_rate</td><td>DECIMAL(5,2)</td><td>Attendance percentage</td></tr>";
    echo "<tr><td>supervisor_feedback</td><td>TEXT</td><td>Supervisor's feedback</td></tr>";
    echo "<tr><td>employee_comments</td><td>TEXT</td><td>Employee's self-assessment</td></tr>";
    echo "<tr><td>improvement_areas</td><td>TEXT</td><td>Areas for improvement</td></tr>";
    echo "<tr><td>strengths</td><td>TEXT</td><td>Employee strengths</td></tr>";
    echo "<tr><td>next_review_date</td><td>DATE</td><td>Next review date</td></tr>";
    echo "<tr><td>status</td><td>ENUM</td><td>draft, completed, approved, needs_improvement</td></tr>";
    echo "<tr><td>created_by</td><td>INT</td><td>Links to users table (supervisor)</td></tr>";
    echo "<tr><td>created_at</td><td>TIMESTAMP</td><td>Creation timestamp</td></tr>";
    echo "<tr><td>updated_at</td><td>TIMESTAMP</td><td>Last update timestamp</td></tr>";
    echo "</table>";
    
    echo "<p><strong>This table connects to:</strong></p>";
    echo "<ul>";
    echo "<li><strong>users table</strong> - via user_id (employee being evaluated)</li>";
    echo "<li><strong>departments table</strong> - via department_id (employee's department)</li>";
    echo "<li><strong>roles table</strong> - via role_id (employee's role)</li>";
    echo "<li><strong>users table</strong> - via created_by (supervisor who created the evaluation)</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "❌ Error creating table: " . $e->getMessage();
}
?>
