<?php
session_start();
require_once '../../config/config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../auth/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['user_role'];

// Get quick stats for the main reports page
try {
    // Sales stats
    $sales_sql = "SELECT COUNT(*) as orders, SUM(total_amount) as total FROM procurement_orders WHERE status = 'completed'";
    $sales_stmt = $conn->prepare($sales_sql);
    $sales_stmt->execute();
    $sales_stats = $sales_stmt->fetch(PDO::FETCH_ASSOC);

    // Inventory stats
    $inventory_sql = "SELECT COUNT(*) as items, SUM(stocks) as stock FROM inventory WHERE status = 'approved'";
    $inventory_stmt = $conn->prepare($inventory_sql);
    $inventory_stmt->execute();
    $inventory_stats = $inventory_stmt->fetch(PDO::FETCH_ASSOC);

    // Finance stats
    $finance_sql = "SELECT SUM(total_amount) as expenses FROM procurement_orders WHERE status = 'completed' AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
    $finance_stmt = $conn->prepare($finance_sql);
    $finance_stmt->execute();
    $finance_stats = $finance_stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $sales_stats = ['orders' => 0, 'total' => 0];
    $inventory_stats = ['items' => 0, 'stock' => 0];
    $finance_stats = ['expenses' => 0];
    error_log("Reports stats error: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #faf2e9;
        }

        .page-title {
            background-color: #f15b31;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }

        .report-card {
            background: linear-gradient(45deg, #f15b31, #d14426);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease;
            text-decoration: none;
            display: block;
        }

        .report-card:hover {
            transform: translateY(-5px);
            color: white;
            text-decoration: none;
        }

        .report-card h4 {
            margin-bottom: 15px;
            font-weight: bold;
        }

        .report-card p {
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .report-card .icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #f15b31;
        }

        .stats-card h6 {
            color: #f15b31;
            margin-bottom: 10px;
        }

        .stats-card h3 {
            color: #333;
            margin: 0;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <div class="container mt-4">
        <h1 class="page-title">📊 Reports Dashboard</h1>

        <!-- Back Button -->
        <div class="row mb-4">
            <div class="col-12 text-end">
                <a href="../admin/admin_dashboard.php" class="btn" style="background-color: #f15b31; color: white;">Back to Dashboard</a>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stats-card">
                    <h6>Total Sales Orders</h6>
                    <h3><?php echo number_format($sales_stats['orders'] ?? 0); ?></h3>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <h6>Inventory Items</h6>
                    <h3><?php echo number_format($inventory_stats['items'] ?? 0); ?></h3>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <h6>Monthly Expenses</h6>
                    <h3>$<?php echo number_format($finance_stats['expenses'] ?? 0, 2); ?></h3>
                </div>
            </div>
        </div>

        <!-- Report Cards -->
        <div class="row">
            <div class="col-md-4">
                <a href="sales_report.php" class="report-card">
                    <div class="icon">📈</div>
                    <h4>Sales Report</h4>
                    <p>View detailed sales analytics</p>
                    <p>Orders, revenue, and trends</p>
                    <small>Click to view detailed sales data</small>
                </a>
            </div>
            <div class="col-md-4">
                <a href="inventory_report.php" class="report-card">
                    <div class="icon">📦</div>
                    <h4>Inventory Report</h4>
                    <p>Track inventory levels and values</p>
                    <p>Stock levels, brands, and products</p>
                    <small>Click to view inventory details</small>
                </a>
            </div>
            <div class="col-md-4">
                <a href="finance_report.php" class="report-card">
                    <div class="icon">💰</div>
                    <h4>Finance Report</h4>
                    <p>Monitor expenses and budgets</p>
                    <p>Department spending and costs</p>
                    <small>Click to view financial data</small>
                </a>
            </div>
        </div>

        <!-- Additional Info -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header" style="background-color: #f15b31; color: white;">
                        <h5>📋 Report Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6 style="color: #f15b31;">📈 Sales Report</h6>
                                <ul>
                                    <li>Monthly sales overview</li>
                                    <li>Order transactions</li>
                                    <li>Customer analytics</li>
                                    <li>Revenue trends</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6 style="color: #f15b31;">📦 Inventory Report</h6>
                                <ul>
                                    <li>Stock levels by brand</li>
                                    <li>Product details</li>
                                    <li>Inventory valuation</li>
                                    <li>Expiry tracking</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6 style="color: #f15b31;">💰 Finance Report</h6>
                                <ul>
                                    <li>Department expenses</li>
                                    <li>Budget allocation</li>
                                    <li>Cost analysis</li>
                                    <li>Financial summaries</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>