<?php
echo "<h2>Testing API Endpoints</h2>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .endpoint { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

// Test inventory data endpoint
echo "<div class='endpoint'>";
echo "<h3>Testing: app/modules/get_inventory_data.php</h3>";
try {
    $url = 'http://localhost/finance/app/modules/get_inventory_data.php';
    $response = file_get_contents($url);
    if ($response !== false) {
        $data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "<div class='success'>";
            echo "<p>✅ Success! Retrieved " . count($data) . " brand records</p>";
            echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT) . "</pre>";
            echo "</div>";
        } else {
            echo "<div class='error'>";
            echo "<p>❌ JSON Error: " . json_last_error_msg() . "</p>";
            echo "<p>Raw response: " . htmlspecialchars($response) . "</p>";
            echo "</div>";
        }
    } else {
        echo "<div class='error'>";
        echo "<p>❌ Failed to fetch data from endpoint</p>";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<p>❌ Exception: " . $e->getMessage() . "</p>";
    echo "</div>";
}
echo "</div>";

// Test low stock endpoint
echo "<div class='endpoint'>";
echo "<h3>Testing: app/modules/get_low_stock.php</h3>";
try {
    $url = 'http://localhost/finance/app/modules/get_low_stock.php';
    $response = file_get_contents($url);
    if ($response !== false) {
        $data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "<div class='success'>";
            echo "<p>✅ Success! Retrieved " . count($data) . " low stock items</p>";
            echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT) . "</pre>";
            echo "</div>";
        } else {
            echo "<div class='error'>";
            echo "<p>❌ JSON Error: " . json_last_error_msg() . "</p>";
            echo "<p>Raw response: " . htmlspecialchars($response) . "</p>";
            echo "</div>";
        }
    } else {
        echo "<div class='error'>";
        echo "<p>❌ Failed to fetch data from endpoint</p>";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<p>❌ Exception: " . $e->getMessage() . "</p>";
    echo "</div>";
}
echo "</div>";

// Test direct database connection
echo "<div class='endpoint'>";
echo "<h3>Testing: Direct Database Connection</h3>";
try {
    require_once 'app/config/config.php';
    
    // Test inventory query
    $sql = "SELECT brand_name, SUM(stocks) as total_stock, COUNT(*) as total_products FROM inventory WHERE status = 'approved' GROUP BY brand_name ORDER BY total_stock DESC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='success'>";
    echo "<p>✅ Direct database connection successful!</p>";
    echo "<p>Found " . count($result) . " brand records in inventory table</p>";
    echo "<pre>" . json_encode($result, JSON_PRETTY_PRINT) . "</pre>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<p>❌ Database Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}
echo "</div>";

echo "<h3>Quick Links</h3>";
echo "<p><a href='app/modules/admin/admin_dashboard.php'>Admin Dashboard</a></p>";
echo "<p><a href='app/modules/reports/reports.php'>Reports Page</a></p>";
echo "<p><a href='app/modules/inventory/inventory_dashboard.php'>Inventory Dashboard</a></p>";
echo "<p><a href='verify_data.php'>Verify Data</a></p>";
?>
