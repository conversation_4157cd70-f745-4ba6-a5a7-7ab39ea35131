<?php
require_once 'app/config/config.php';

echo "<h2>Inserting HR Sample Data...</h2>";

try {
    // Create employee_performance table if it doesn't exist
    $conn->exec("CREATE TABLE IF NOT EXISTS employee_performance (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        department_id INT,
        evaluation_date DATE NOT NULL,
        performance_score DECIMAL(3,2) NOT NULL,
        goals_achieved INT DEFAULT 0,
        total_goals INT DEFAULT 0,
        attendance_rate DECIMAL(5,2) DEFAULT 100.00,
        supervisor_feedback TEXT,
        status ENUM('draft', 'completed', 'approved', 'needs_improvement') DEFAULT 'draft',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    echo "✅ Employee Performance table created/verified!<br>";
    
    // Check if we have users and departments
    $users_count = $conn->query("SELECT COUNT(*) FROM users")->fetchColumn();
    $departments_count = $conn->query("SELECT COUNT(*) FROM departments")->fetchColumn();
    
    echo "📊 Found $users_count users and $departments_count departments<br>";
    
    // Insert sample performance data
    $sample_data = [
        [1, 1, '2024-01-15', 4.5, 8, 10, 95.50, 'Excellent performance this quarter. Shows great leadership skills and consistently meets deadlines.', 'approved'],
        [2, 2, '2024-01-20', 3.8, 6, 8, 88.75, 'Good performance with room for improvement in communication and time management.', 'completed'],
        [3, 3, '2024-01-25', 4.2, 7, 9, 92.30, 'Consistently meets expectations and shows initiative in problem-solving.', 'approved'],
        [4, 1, '2024-02-01', 3.5, 5, 10, 85.60, 'Performance is satisfactory but needs more focus on meeting project deadlines.', 'needs_improvement'],
        [5, 2, '2024-02-05', 4.8, 9, 10, 98.20, 'Outstanding performance. Exceeds all expectations and mentors junior staff.', 'approved'],
        [6, 3, '2024-02-10', 4.0, 7, 8, 90.45, 'Solid performance with good technical skills and team collaboration.', 'completed'],
        [1, 1, '2024-02-15', 3.9, 6, 9, 87.30, 'Good improvement from last evaluation. Communication skills have developed well.', 'completed'],
        [2, 2, '2024-02-20', 4.1, 8, 10, 94.80, 'Strong performance with excellent attention to detail and quality work.', 'approved']
    ];
    
    $insert_sql = "INSERT INTO employee_performance 
                   (user_id, department_id, evaluation_date, performance_score, goals_achieved, total_goals, 
                    attendance_rate, supervisor_feedback, status) 
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($insert_sql);
    
    $inserted = 0;
    foreach ($sample_data as $data) {
        try {
            $stmt->execute($data);
            $inserted++;
        } catch (PDOException $e) {
            echo "⚠️ Could not insert record: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "✅ Inserted $inserted employee performance records!<br>";
    
    // Show summary
    echo "<h3>📋 Summary:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 20px 0; width: 100%;'>";
    echo "<tr style='background-color: #f15b31; color: white;'>";
    echo "<th>Employee</th><th>Department</th><th>Date</th><th>Score</th><th>Goals</th><th>Attendance</th><th>Status</th></tr>";
    
    $summary_sql = "
        SELECT ep.*, 
               CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as employee_name,
               u.username,
               d.name as department_name
        FROM employee_performance ep
        LEFT JOIN users u ON ep.user_id = u.id
        LEFT JOIN departments d ON ep.department_id = d.id
        ORDER BY ep.evaluation_date DESC
    ";
    
    $result = $conn->query($summary_sql);
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        $employee_display = !empty(trim($row['employee_name'])) ? $row['employee_name'] : $row['username'];
        echo "<tr>";
        echo "<td>" . htmlspecialchars($employee_display) . "</td>";
        echo "<td>" . htmlspecialchars($row['department_name'] ?? 'N/A') . "</td>";
        echo "<td>" . date('M d, Y', strtotime($row['evaluation_date'])) . "</td>";
        echo "<td>" . number_format($row['performance_score'], 1) . "/5.0</td>";
        echo "<td>" . $row['goals_achieved'] . "/" . $row['total_goals'] . "</td>";
        echo "<td>" . number_format($row['attendance_rate'], 1) . "%</td>";
        echo "<td>" . ucfirst(str_replace('_', ' ', $row['status'])) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>🎯 What's Connected:</h3>";
    echo "<ul>";
    echo "<li><strong>employee_performance.user_id</strong> → <strong>users.id</strong> (Employee being evaluated)</li>";
    echo "<li><strong>employee_performance.department_id</strong> → <strong>departments.id</strong> (Employee's department)</li>";
    echo "</ul>";
    
    echo "<h3>🔗 Access Links:</h3>";
    echo "<ul>";
    echo "<li><a href='app/modules/user/hr_reports.php' target='_blank'>HR Reports Dashboard (Admin Only)</a></li>";
    echo "<li><a href='app/modules/user/admin_employees.php' target='_blank'>Employee Management (Admin Only)</a></li>";
    echo "<li><a href='app/modules/admin/admin_dashboard.php' target='_blank'>Admin Dashboard</a></li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage();
}
?>
