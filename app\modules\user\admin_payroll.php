<?php
session_start();
require_once '../../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header("Location: ../auth/login.php");
    exit();
}

// Create payroll table if it doesn't exist
try {
    $conn->exec("CREATE TABLE IF NOT EXISTS payroll (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        pay_rate DECIMAL(10,2) NOT NULL,
        hours_worked DECIMAL(10,2) NOT NULL,
        gross_pay DECIMAL(10,2) NOT NULL,
        deductions DECIMAL(10,2) NOT NULL,
        net_pay DECIMAL(10,2) NOT NULL,
        pay_period VARCHAR(50) NOT NULL,
        payment_date DATE NOT NULL,
        status VARCHAR(20) NOT NULL DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
} catch (PDOException $e) {
    // Continue if error
}

// Test simple query first
try {
    $test_stmt = $conn->prepare("SELECT COUNT(*) as total FROM payroll");
    $test_stmt->execute();
    $test_result = $test_stmt->fetch(PDO::FETCH_ASSOC);
    $total_payroll_count = $test_result['total'];

    // Test if we can get raw payroll data
    $raw_stmt = $conn->prepare("SELECT * FROM payroll LIMIT 3");
    $raw_stmt->execute();
    $raw_payroll = $raw_stmt->fetchAll(PDO::FETCH_ASSOC);

    // Test users table
    $users_stmt = $conn->prepare("SELECT COUNT(*) as user_count FROM users WHERE id = 16");
    $users_stmt->execute();
    $users_result = $users_stmt->fetch(PDO::FETCH_ASSOC);
    $user_16_exists = $users_result['user_count'];
} catch (PDOException $e) {
    $total_payroll_count = 0;
    $raw_payroll = [];
    $user_16_exists = 0;
}

// Get payroll records from database - simplified query first
try {
    // Try simple query first without JOIN
    $stmt = $conn->prepare("SELECT * FROM payroll ORDER BY payment_date DESC");
    $stmt->execute();
    $simple_payroll = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Now try to get user info for each record
    $payroll_records = [];
    foreach ($simple_payroll as $payroll) {
        $user_stmt = $conn->prepare("SELECT username, first_name, last_name, department, role FROM users WHERE id = ?");
        $user_stmt->execute([$payroll['user_id']]);
        $user = $user_stmt->fetch(PDO::FETCH_ASSOC);

        $payroll['employee_name'] = '';
        $payroll['username'] = '';
        $payroll['department'] = '';
        $payroll['role'] = '';

        if ($user) {
            $payroll['employee_name'] = trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? ''));
            $payroll['username'] = $user['username'] ?? '';
            $payroll['department'] = $user['department'] ?? '';
            $payroll['role'] = $user['role'] ?? '';
        }

        $payroll_records[] = $payroll;
    }

    // Debug: Check if we got data
    error_log("Payroll records count: " . count($payroll_records));
    if (count($payroll_records) > 0) {
        error_log("First record: " . print_r($payroll_records[0], true));
    }
} catch (PDOException $e) {
    $payroll_records = [];
    error_log("Payroll query error: " . $e->getMessage());
}

// Calculate payroll statistics
$total_gross_pay = 0;
$total_net_pay = 0;
$total_deductions = 0;
$processed_count = 0;
$pending_count = 0;

foreach ($payroll_records as $record) {
    $total_gross_pay += $record['gross_pay'];
    $total_net_pay += $record['net_pay'];
    $total_deductions += $record['deductions'];

    if ($record['status'] == 'processed') {
        $processed_count++;
    } else {
        $pending_count++;
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Payroll Overview</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #faf2e9;
        }

        .page-title {
            background-color: #f15b31;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }

        .card-header {
            background-color: #f15b31 !important;
            color: white !important;
        }

        .btn-primary {
            background-color: #f15b31;
            border-color: #f15b31;
        }

        .btn-primary:hover {
            background-color: #d14118;
            border-color: #d14118;
        }

        .table th {
            background-color: #f15b31;
            color: white;
        }

        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #f15b31;
        }

        .stats-card h6 {
            color: #f15b31;
            margin-bottom: 10px;
        }

        .stats-card h3 {
            color: #333;
            margin: 0;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <div class="container mt-4">
        <h1 class="page-title">💰 Admin - Payroll Overview</h1>

        <!-- Back Button -->
        <div class="row mb-4">
            <div class="col-12 text-end">
                <a href="hr_reports.php" class="btn" style="background-color: #f15b31; color: white;">Back to HR Reports</a>
            </div>
        </div>

        <!-- Payroll Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <h6>Total Gross Pay</h6>
                    <h3>₱<?php echo number_format($total_gross_pay, 2); ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h6>Total Net Pay</h6>
                    <h3>₱<?php echo number_format($total_net_pay, 2); ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h6>Total Deductions</h6>
                    <h3>₱<?php echo number_format($total_deductions, 2); ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h6>Processed/Pending</h6>
                    <h3><?php echo $processed_count; ?>/<?php echo $pending_count; ?></h3>
                </div>
            </div>
        </div>

        <!-- Payroll Records -->
        <div class="card mb-4">
            <div class="card-header">
                <h4>All Payroll Records</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Employee</th>
                                <th>Department</th>
                                <th>Role</th>
                                <th>Pay Rate</th>
                                <th>Hours</th>
                                <th>Gross Pay</th>
                                <th>Deductions</th>
                                <th>Net Pay</th>
                                <th>Pay Period</th>
                                <th>Payment Date</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (count($payroll_records) > 0): ?>
                                <?php foreach ($payroll_records as $record): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($record['id']); ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars(!empty(trim($record['employee_name'])) ? $record['employee_name'] : $record['username']); ?></strong>
                                        </td>
                                        <td><?php echo htmlspecialchars($record['department'] ?? 'N/A'); ?></td>
                                        <td><?php echo htmlspecialchars($record['role'] ?? 'N/A'); ?></td>
                                        <td>₱<?php echo number_format($record['pay_rate'], 2); ?></td>
                                        <td><?php echo number_format($record['hours_worked'], 1); ?> hrs</td>
                                        <td>₱<?php echo number_format($record['gross_pay'], 2); ?></td>
                                        <td>₱<?php echo number_format($record['deductions'], 2); ?></td>
                                        <td><strong>₱<?php echo number_format($record['net_pay'], 2); ?></strong></td>
                                        <td><?php echo htmlspecialchars($record['pay_period']); ?></td>
                                        <td><?php echo date('M d, Y', strtotime($record['payment_date'])); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $record['status'] == 'processed' ? 'success' : 'warning'; ?>">
                                                <?php echo ucfirst($record['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="12" class="text-center">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle"></i> No payroll records found.
                                            <br><small>Debug Info:</small>
                                            <br><small>Records count: <?php echo count($payroll_records); ?></small>
                                            <br><small>Total in DB: <?php echo $total_payroll_count ?? 'Unknown'; ?></small>
                                            <br><small>User 16 exists: <?php echo $user_16_exists ?? 'Unknown'; ?></small>
                                            <br><small>Raw payroll count: <?php echo count($raw_payroll ?? []); ?></small>
                                            <?php if (!empty($raw_payroll)): ?>
                                                <br><small>Sample data: ID=<?php echo $raw_payroll[0]['id']; ?>, User=<?php echo $raw_payroll[0]['user_id']; ?>, Pay=<?php echo $raw_payroll[0]['pay_rate']; ?></small>
                                            <?php endif; ?>
                                            <br><small>Database connection: <?php echo isset($conn) ? 'Connected' : 'Not connected'; ?></small>
                                            <br><small>Query executed: <?php echo isset($stmt) ? 'Yes' : 'No'; ?></small>
                                            <br><a href="../finance/payroll.php">Add payroll records</a> to see data here.
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Back to Dashboard Button -->
        <div class="text-center mt-4 mb-4">
            <a href="../admin/admin_dashboard.php" class="btn btn-primary">Back to Dashboard</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>