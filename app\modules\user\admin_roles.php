<?php
session_start();
require_once '../../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header("Location: ../auth/login.php");
    exit();
}

// Get all roles from database
try {
    $stmt = $conn->prepare("SELECT * FROM roles ORDER BY name");
    $stmt->execute();
    $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $roles = [];
}

// Get users count per role
try {
    $stmt = $conn->prepare("
        SELECT role, COUNT(*) as user_count 
        FROM users 
        GROUP BY role
    ");
    $stmt->execute();
    $role_user_counts = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
} catch (PDOException $e) {
    $role_user_counts = [];
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Roles Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #faf2e9;
        }

        .page-title {
            background-color: #f15b31;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }

        .card-header {
            background-color: #f15b31 !important;
            color: white !important;
        }

        .btn-primary {
            background-color: #f15b31;
            border-color: #f15b31;
        }

        .btn-primary:hover {
            background-color: #d14118;
            border-color: #d14118;
        }

        .table th {
            background-color: #f15b31;
            color: white;
        }
    </style>
</head>

<body>
    <div class="container mt-4">
        <h1 class="page-title">🔐 Admin - Roles Management</h1>

        <!-- Back Button -->
        <div class="row mb-4">
            <div class="col-12 text-end">
                <a href="hr_reports.php" class="btn" style="background-color: #f15b31; color: white;">Back to HR Reports</a>
            </div>
        </div>

        <!-- Roles List -->
        <div class="card mb-4">
            <div class="card-header">
                <h4>All Roles</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Role Name</th>
                                <th>Description</th>
                                <th>Permissions</th>
                                <th>User Count</th>
                                <th>Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($roles as $role): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($role['id']); ?></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($role['name']); ?></strong>
                                    </td>
                                    <td><?php echo htmlspecialchars($role['description'] ?? 'N/A'); ?></td>
                                    <td>
                                        <?php if (!empty($role['permissions'])): ?>
                                            <small class="text-muted"><?php echo htmlspecialchars($role['permissions']); ?></small>
                                        <?php else: ?>
                                            <span class="text-muted">No permissions set</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo $role_user_counts[$role['name']] ?? 0; ?> users
                                        </span>
                                    </td>
                                    <td><?php echo isset($role['created_at']) ? date('M d, Y', strtotime($role['created_at'])) : 'N/A'; ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Role Statistics -->
        <div class="card mb-4">
            <div class="card-header">
                <h4>Role Distribution</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($roles as $role): ?>
                        <div class="col-md-4 mb-3">
                            <div class="card border-left-primary">
                                <div class="card-body">
                                    <h6 class="card-title" style="color: #f15b31;"><?php echo htmlspecialchars($role['name']); ?></h6>
                                    <p class="card-text">
                                        <strong>Users:</strong> <?php echo $role_user_counts[$role['name']] ?? 0; ?><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($role['description'] ?? 'No description'); ?></small>
                                    </p>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Back to Dashboard Button -->
        <div class="text-center mt-4 mb-4">
            <a href="../admin/admin_dashboard.php" class="btn btn-primary">Back to Dashboard</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>
