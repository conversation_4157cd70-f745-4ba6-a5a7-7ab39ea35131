<!DOCTYPE html>
<html>
<head>
    <title>Debug Reports Tables</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-box { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; background: #f9f9f9; }
        .error { background: #ffebee; border-color: #f44336; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        .warning { background: #fff3e0; border-color: #ff9800; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f15b31; color: white; }
        .btn { background: #f15b31; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
    </style>
</head>
<body>
    <h1>🔍 Debug Reports Tables</h1>
    
    <?php
    require_once 'app/config/config.php';
    
    echo "<div class='debug-box'>";
    echo "<h3>1. Database Connection Test</h3>";
    try {
        $test_query = $conn->prepare("SELECT 1 as test");
        $test_query->execute();
        $result = $test_query->fetch();
        if ($result['test'] == 1) {
            echo "<p class='success'>✅ Database connection working</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    echo "<div class='debug-box'>";
    echo "<h3>2. Check Tables Exist</h3>";
    
    $tables_to_check = ['procurement_orders', 'inventory', 'users', 'departments'];
    foreach ($tables_to_check as $table) {
        try {
            $check_sql = "SELECT COUNT(*) as count FROM $table LIMIT 1";
            $stmt = $conn->prepare($check_sql);
            $stmt->execute();
            $result = $stmt->fetch();
            echo "<p class='success'>✅ Table '$table' exists with " . $result['count'] . " records</p>";
        } catch (Exception $e) {
            echo "<p class='error'>❌ Table '$table' error: " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
    
    echo "<div class='debug-box'>";
    echo "<h3>3. Sales Data Debug</h3>";
    try {
        // Test the exact query from sales_report.php
        $sales_sql = "SELECT
            COUNT(*) as total_orders,
            SUM(total_amount) as total_sales,
            AVG(total_amount) as average_order_value
          FROM procurement_orders
          WHERE status = 'completed'
          AND created_at BETWEEN '2024-01-01' AND '2024-12-31 23:59:59'";
        
        $stmt = $conn->prepare($sales_sql);
        $stmt->execute();
        $sales_result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<p><strong>Sales Query Result:</strong></p>";
        echo "<p>Total Orders: " . ($sales_result['total_orders'] ?? 'NULL') . "</p>";
        echo "<p>Total Sales: $" . number_format($sales_result['total_sales'] ?? 0, 2) . "</p>";
        echo "<p>Average Order: $" . number_format($sales_result['average_order_value'] ?? 0, 2) . "</p>";
        
        // Test transactions query
        $trans_sql = "SELECT
            po.order_number,
            po.created_at as order_date,
            po.total_amount,
            po.status
          FROM procurement_orders po
          WHERE po.created_at BETWEEN '2024-01-01' AND '2024-12-31 23:59:59'
          ORDER BY po.created_at DESC
          LIMIT 5";
        
        $trans_stmt = $conn->prepare($trans_sql);
        $trans_stmt->execute();
        $transactions = $trans_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p><strong>Sample Transactions (" . count($transactions) . " found):</strong></p>";
        if (count($transactions) > 0) {
            echo "<table>";
            echo "<tr><th>Order Number</th><th>Date</th><th>Amount</th><th>Status</th></tr>";
            foreach ($transactions as $trans) {
                echo "<tr>";
                echo "<td>" . $trans['order_number'] . "</td>";
                echo "<td>" . $trans['order_date'] . "</td>";
                echo "<td>$" . number_format($trans['total_amount'], 2) . "</td>";
                echo "<td>" . $trans['status'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='warning'>⚠️ No transactions found</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Sales query error: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    echo "<div class='debug-box'>";
    echo "<h3>4. Inventory Data Debug</h3>";
    try {
        // Test inventory query
        $inv_sql = "SELECT
            brand_name,
            COUNT(*) as total_products,
            SUM(stocks) as total_stock,
            SUM(stocks * price) as total_value
          FROM inventory
          GROUP BY brand_name
          ORDER BY total_value DESC
          LIMIT 5";
        
        $inv_stmt = $conn->prepare($inv_sql);
        $inv_stmt->execute();
        $inventory = $inv_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p><strong>Brand Inventory (" . count($inventory) . " brands found):</strong></p>";
        if (count($inventory) > 0) {
            echo "<table>";
            echo "<tr><th>Brand</th><th>Products</th><th>Stock</th><th>Value</th></tr>";
            foreach ($inventory as $brand) {
                echo "<tr>";
                echo "<td>" . $brand['brand_name'] . "</td>";
                echo "<td>" . $brand['total_products'] . "</td>";
                echo "<td>" . $brand['total_stock'] . "</td>";
                echo "<td>$" . number_format($brand['total_value'], 2) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='warning'>⚠️ No inventory found</p>";
        }
        
        // Test detailed products
        $prod_sql = "SELECT prod_name, brand_name, price, stocks FROM inventory LIMIT 5";
        $prod_stmt = $conn->prepare($prod_sql);
        $prod_stmt->execute();
        $products = $prod_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p><strong>Sample Products (" . count($products) . " found):</strong></p>";
        if (count($products) > 0) {
            echo "<table>";
            echo "<tr><th>Product</th><th>Brand</th><th>Price</th><th>Stock</th></tr>";
            foreach ($products as $prod) {
                echo "<tr>";
                echo "<td>" . $prod['prod_name'] . "</td>";
                echo "<td>" . $prod['brand_name'] . "</td>";
                echo "<td>$" . number_format($prod['price'], 2) . "</td>";
                echo "<td>" . $prod['stocks'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='warning'>⚠️ No products found</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Inventory query error: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    echo "<div class='debug-box'>";
    echo "<h3>5. Quick Fix</h3>";
    echo "<p>If tables are empty, the reports will show empty tables even with fallback data.</p>";
    echo "<p><strong>Solutions:</strong></p>";
    echo "<ul>";
    echo "<li>Add real data to the database tables</li>";
    echo "<li>Or modify the reports to always show sample data</li>";
    echo "</ul>";
    echo "<a href='add_sample_data_simple.php' class='btn'>🔧 Add Sample Data to Database</a>";
    echo "<a href='fix_reports_with_sample_data.php' class='btn'>🔧 Force Sample Data in Reports</a>";
    echo "</div>";
    
    echo "<div class='debug-box'>";
    echo "<h3>6. Test Reports Again</h3>";
    echo "<a href='app/modules/reports/reports.php' class='btn'>📊 Main Reports</a>";
    echo "<a href='app/modules/reports/sales_report.php' class='btn'>📈 Sales Report</a>";
    echo "<a href='app/modules/reports/inventory_report.php' class='btn'>📦 Inventory Report</a>";
    echo "<a href='app/modules/reports/finance_report.php' class='btn'>💰 Finance Report</a>";
    echo "</div>";
    ?>
    
    <script>
        console.log('🔍 Debug reports tables loaded');
    </script>
</body>
</html>
