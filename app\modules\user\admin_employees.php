<?php
session_start();
require_once '../../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header("Location: ../auth/login.php");
    exit();
}

// Create employee_performance table if it doesn't exist
try {
    $conn->exec("CREATE TABLE IF NOT EXISTS employee_performance (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        department_id INT,
        evaluation_date DATE NOT NULL,
        performance_score DECIMAL(3,2) NOT NULL,
        goals_achieved INT DEFAULT 0,
        total_goals INT DEFAULT 0,
        attendance_rate DECIMAL(5,2) DEFAULT 100.00,
        supervisor_feedback TEXT,
        status ENUM('draft', 'completed', 'approved', 'needs_improvement') DEFAULT 'draft',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
} catch (PDOException $e) {
    // Table creation failed, but continue
}

// Process form submission for adding employee performance
if (isset($_POST['add_performance'])) {
    $user_id = $_POST['user_id'];
    $department_id = $_POST['department_id'];
    $evaluation_date = $_POST['evaluation_date'];
    $performance_score = $_POST['performance_score'];
    $goals_achieved = $_POST['goals_achieved'];
    $total_goals = $_POST['total_goals'];
    $attendance_rate = $_POST['attendance_rate'];
    $supervisor_feedback = $_POST['supervisor_feedback'];
    $status = $_POST['status'];

    try {
        $stmt = $conn->prepare("INSERT INTO employee_performance (user_id, department_id, evaluation_date, performance_score, goals_achieved, total_goals, attendance_rate, supervisor_feedback, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
        if ($stmt->execute([$user_id, $department_id, $evaluation_date, $performance_score, $goals_achieved, $total_goals, $attendance_rate, $supervisor_feedback, $status])) {
            $success_message = "Employee performance record added successfully";
        }
    } catch (PDOException $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get all users
try {
    $stmt = $conn->prepare("SELECT u.*, d.name as department_name, r.name as role_name FROM users u
                           LEFT JOIN departments d ON u.department = d.name
                           LEFT JOIN roles r ON u.role = r.name");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $users = [];
}

// Get all departments for dropdown
try {
    $stmt = $conn->prepare("SELECT * FROM departments");
    $stmt->execute();
    $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $departments = [];
}

// Get employee performance records with user and department names
try {
    $stmt = $conn->prepare("
        SELECT ep.*,
               CONCAT(u.first_name, ' ', u.last_name) as employee_name,
               u.username,
               d.name as department_name
        FROM employee_performance ep
        LEFT JOIN users u ON ep.user_id = u.id
        LEFT JOIN departments d ON ep.department_id = d.id
        ORDER BY ep.evaluation_date DESC
    ");
    $stmt->execute();
    $performance_records = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $performance_records = [];
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Employee Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #faf2e9;
        }

        .page-title {
            background-color: #f15b31;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }

        .card-header {
            background-color: #f15b31 !important;
            color: white !important;
        }

        .btn-primary {
            background-color: #f15b31;
            border-color: #f15b31;
        }

        .btn-primary:hover {
            background-color: #d14118;
            border-color: #d14118;
        }

        .table th {
            background-color: #f15b31;
            color: white;
        }
    </style>
</head>

<body>
    <div class="container mt-4">
        <h1 class="page-title">👥 Admin - Employee Management</h1>

        <!-- Back Button -->
        <div class="row mb-4">
            <div class="col-12 text-end">
                <a href="hr_reports.php" class="btn" style="background-color: #f15b31; color: white;">Back to HR Reports</a>
            </div>
        </div>

        <?php if (isset($success_message)): ?>
            <div class="alert alert-success"><?php echo $success_message; ?></div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger"><?php echo $error_message; ?></div>
        <?php endif; ?>

        <!-- Employees List -->
        <div class="card mb-4">
            <div class="card-header">
                <h4>All Employees</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Department</th>
                                <th>Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($user['id']); ?></td>
                                    <td><?php echo htmlspecialchars(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '')); ?></td>
                                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td><?php echo htmlspecialchars($user['role']); ?></td>
                                    <td><?php echo htmlspecialchars($user['department']); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Add Employee Performance Form -->
        <div class="card mb-4">
            <div class="card-header">
                <h4>Add Employee Performance Record</h4>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="user_id" class="form-label">Employee</label>
                                <select class="form-control" id="user_id" name="user_id" required>
                                    <option value="">Select Employee</option>
                                    <?php foreach ($users as $user): ?>
                                        <option value="<?php echo $user['id']; ?>">
                                            <?php echo htmlspecialchars(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '') . ' (' . $user['username'] . ')'); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="department_id" class="form-label">Department</label>
                                <select class="form-control" id="department_id" name="department_id">
                                    <option value="">Select Department</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?php echo $dept['id']; ?>"><?php echo htmlspecialchars($dept['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="evaluation_date" class="form-label">Evaluation Date</label>
                                <input type="date" class="form-control" id="evaluation_date" name="evaluation_date" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="performance_score" class="form-label">Performance Score (1-5)</label>
                                <input type="number" class="form-control" id="performance_score" name="performance_score" min="1" max="5" step="0.1" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="attendance_rate" class="form-label">Attendance Rate (%)</label>
                                <input type="number" class="form-control" id="attendance_rate" name="attendance_rate" min="0" max="100" step="0.1" value="100">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="goals_achieved" class="form-label">Goals Achieved</label>
                                <input type="number" class="form-control" id="goals_achieved" name="goals_achieved" min="0" value="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="total_goals" class="form-label">Total Goals</label>
                                <input type="number" class="form-control" id="total_goals" name="total_goals" min="0" value="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-control" id="status" name="status" required>
                                    <option value="draft">Draft</option>
                                    <option value="completed">Completed</option>
                                    <option value="approved">Approved</option>
                                    <option value="needs_improvement">Needs Improvement</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="supervisor_feedback" class="form-label">Supervisor Feedback</label>
                        <textarea class="form-control" id="supervisor_feedback" name="supervisor_feedback" rows="3"></textarea>
                    </div>
                    <button type="submit" name="add_performance" class="btn btn-primary">Add Performance Record</button>
                </form>
            </div>
        </div>

        <!-- Employee Performance Records -->
        <div class="card mb-4">
            <div class="card-header">
                <h4>Employee Performance Records</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Employee</th>
                                <th>Department</th>
                                <th>Evaluation Date</th>
                                <th>Score</th>
                                <th>Goals</th>
                                <th>Attendance</th>
                                <th>Status</th>
                                <th>Feedback</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($performance_records as $record): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($record['id']); ?></td>
                                    <td><?php echo htmlspecialchars($record['employee_name'] ?? $record['username']); ?></td>
                                    <td><?php echo htmlspecialchars($record['department_name'] ?? 'N/A'); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($record['evaluation_date'])); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $record['performance_score'] >= 4 ? 'success' : ($record['performance_score'] >= 3 ? 'warning' : 'danger'); ?>">
                                            <?php echo number_format($record['performance_score'], 1); ?>/5.0
                                        </span>
                                    </td>
                                    <td><?php echo $record['goals_achieved']; ?>/<?php echo $record['total_goals']; ?></td>
                                    <td><?php echo number_format($record['attendance_rate'], 1); ?>%</td>
                                    <td>
                                        <span class="badge bg-<?php
                                                                echo $record['status'] == 'approved' ? 'success' : ($record['status'] == 'completed' ? 'info' : ($record['status'] == 'needs_improvement' ? 'danger' : 'secondary'));
                                                                ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $record['status'])); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (!empty($record['supervisor_feedback'])): ?>
                                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#feedbackModal<?php echo $record['id']; ?>">
                                                View Feedback
                                            </button>

                                            <!-- Feedback Modal -->
                                            <div class="modal fade" id="feedbackModal<?php echo $record['id']; ?>" tabindex="-1">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title">Supervisor Feedback</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <p><strong>Employee:</strong> <?php echo htmlspecialchars($record['employee_name']); ?></p>
                                                            <p><strong>Date:</strong> <?php echo date('M d, Y', strtotime($record['evaluation_date'])); ?></p>
                                                            <p><strong>Feedback:</strong></p>
                                                            <p><?php echo nl2br(htmlspecialchars($record['supervisor_feedback'])); ?></p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">No feedback</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Back to Dashboard Button -->
        <div class="text-center mt-4 mb-4">
            <a href="../admin/admin_dashboard.php" class="btn btn-primary">Back to Dashboard</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>