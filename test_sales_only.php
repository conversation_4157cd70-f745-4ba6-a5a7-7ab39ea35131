<?php
require_once 'app/config/config.php';

echo "<h1>Sales Data Debug</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .box{border:1px solid #ccc;padding:15px;margin:10px 0;} .error{background:#ffebee;} .success{background:#e8f5e8;}</style>";

// Set up the same variables as the dashboard
$group_by = "DATE_FORMAT(created_at, '%Y-%m')";
$start_date = date('Y-m-01');
$end_date = date('Y-m-t');

echo "<div class='box'>";
echo "<h3>1. Date Range</h3>";
echo "<p>Start: $start_date</p>";
echo "<p>End: $end_date</p>";
echo "<p>Group by: $group_by</p>";
echo "</div>";

echo "<div class='box'>";
echo "<h3>2. Check if procurement_orders table exists</h3>";
try {
    $stmt = $conn->prepare("SHOW TABLES LIKE 'procurement_orders'");
    $stmt->execute();
    $result = $stmt->fetch();
    if ($result) {
        echo "<p class='success'>✅ procurement_orders table exists</p>";
    } else {
        echo "<p class='error'>❌ procurement_orders table does NOT exist</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Error checking table: " . $e->getMessage() . "</p>";
}
echo "</div>";

echo "<div class='box'>";
echo "<h3>3. Check all orders in procurement_orders</h3>";
try {
    $stmt = $conn->prepare("SELECT COUNT(*) as total, status, SUM(total_amount) as sum FROM procurement_orders GROUP BY status");
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($results) > 0) {
        echo "<table border='1' style='border-collapse:collapse;width:100%;'>";
        echo "<tr><th>Status</th><th>Count</th><th>Total Amount</th></tr>";
        foreach ($results as $row) {
            echo "<tr><td>" . $row['status'] . "</td><td>" . $row['total'] . "</td><td>$" . number_format($row['sum'], 2) . "</td></tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>❌ No orders found in procurement_orders table</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
}
echo "</div>";

echo "<div class='box'>";
echo "<h3>4. Test the exact sales query</h3>";
try {
    $sales_sql = "SELECT
        " . $group_by . " as period,
        COUNT(*) as total_orders,
        SUM(total_amount) as total_sales,
        AVG(total_amount) as average_order_value
      FROM procurement_orders
      WHERE created_at BETWEEN :start_date AND :end_date
      AND status = 'completed'
      GROUP BY " . $group_by . "
      ORDER BY period DESC";

    echo "<p><strong>SQL Query:</strong></p>";
    echo "<pre>" . str_replace([':start_date', ':end_date'], ["'$start_date'", "'{$end_date} 23:59:59'"], $sales_sql) . "</pre>";

    $sales_stmt = $conn->prepare($sales_sql);
    $sales_stmt->bindParam(':start_date', $start_date);
    $end_date_with_time = $end_date . ' 23:59:59';
    $sales_stmt->bindParam(':end_date', $end_date_with_time);
    $sales_stmt->execute();
    $sales_data = $sales_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Results:</strong></p>";
    if (count($sales_data) > 0) {
        echo "<p class='success'>✅ Found " . count($sales_data) . " sales periods</p>";
        echo "<pre>" . json_encode($sales_data, JSON_PRETTY_PRINT) . "</pre>";
        
        // Calculate totals
        $total = 0;
        $orders = 0;
        foreach ($sales_data as $row) {
            $total += $row['total_sales'];
            $orders += $row['total_orders'];
        }
        
        echo "<h4>Dashboard Values:</h4>";
        echo "<p>Total Sales: $" . number_format($total, 2) . "</p>";
        echo "<p>Total Orders: " . number_format($orders) . "</p>";
        echo "<p>Average Order Value: $" . ($orders > 0 ? number_format($total / $orders, 2) : '0.00') . "</p>";
        
    } else {
        echo "<p class='error'>❌ No sales data found for current month</p>";
        echo "<p>This means either:</p>";
        echo "<ul>";
        echo "<li>No orders with status = 'completed'</li>";
        echo "<li>No orders in current month ($start_date to $end_date)</li>";
        echo "<li>Orders exist but in different date range</li>";
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Sales query failed: " . $e->getMessage() . "</p>";
}
echo "</div>";

echo "<div class='box'>";
echo "<h3>5. Try broader date range</h3>";
try {
    $broad_sql = "SELECT
        COUNT(*) as total_orders,
        SUM(total_amount) as total_sales,
        AVG(total_amount) as average_order_value,
        MIN(created_at) as earliest,
        MAX(created_at) as latest
      FROM procurement_orders
      WHERE status = 'completed'";

    $stmt = $conn->prepare($broad_sql);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result && $result['total_orders'] > 0) {
        echo "<p class='success'>✅ Found completed orders in broader range:</p>";
        echo "<p>Total Orders: " . $result['total_orders'] . "</p>";
        echo "<p>Total Sales: $" . number_format($result['total_sales'], 2) . "</p>";
        echo "<p>Average: $" . number_format($result['average_order_value'], 2) . "</p>";
        echo "<p>Date Range: " . $result['earliest'] . " to " . $result['latest'] . "</p>";
    } else {
        echo "<p class='error'>❌ No completed orders found at all</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
}
echo "</div>";

echo "<div class='box'>";
echo "<h3>6. Quick Fix</h3>";
echo "<p>If no data found, let's add some test data:</p>";
echo "<p><a href='add_sample_data_simple.php' style='background:#f15b31;color:white;padding:10px;text-decoration:none;border-radius:5px;'>Add Sample Data</a></p>";
echo "</div>";
?>
