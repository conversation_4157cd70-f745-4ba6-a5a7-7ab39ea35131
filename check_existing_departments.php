<!DOCTYPE html>
<html>
<head>
    <title>Check Existing Departments</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #faf2e9; }
        .status-box { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        .error { background: #ffebee; border-color: #f44336; }
        .warning { background: #fff3e0; border-color: #ff9800; }
        .info { background: #e3f2fd; border-color: #2196f3; }
        .btn { background: #f15b31; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
        .page-title { background-color: #f15b31; color: white; padding: 20px; border-radius: 5px; text-align: center; margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f15b31; color: white; }
    </style>
</head>
<body>
    <h1 class="page-title">🔍 Check Existing Departments</h1>
    
    <?php
    require_once 'app/config/config.php';
    
    echo "<div class='warning-box'>";
    echo "<h3>⚠️ Oops! Sorry par!</h3>";
    echo "<p>Hindi ko na-check kung may existing departments ka na. Let me check what you already have...</p>";
    echo "</div>";
    
    try {
        // Check existing departments
        $dept_sql = "SELECT * FROM departments ORDER BY id";
        $dept_stmt = $conn->prepare($dept_sql);
        $dept_stmt->execute();
        $existing_departments = $dept_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='info-box'>";
        echo "<h3>📋 Your Existing Departments:</h3>";
        
        if (count($existing_departments) > 0) {
            echo "<table>";
            echo "<tr><th>ID</th><th>Name</th><th>Description</th><th>Created</th></tr>";
            foreach ($existing_departments as $dept) {
                echo "<tr>";
                echo "<td>" . $dept['id'] . "</td>";
                echo "<td>" . htmlspecialchars($dept['name']) . "</td>";
                echo "<td>" . htmlspecialchars($dept['description'] ?? 'N/A') . "</td>";
                echo "<td>" . ($dept['created_at'] ?? 'N/A') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<p><strong>Total departments found:</strong> " . count($existing_departments) . "</p>";
        } else {
            echo "<p>No departments found in the database.</p>";
        }
        echo "</div>";
        
        // Check for duplicates that I might have added
        $duplicate_check = [];
        $my_added_departments = ['Finance', 'Procurement', 'Logistics', 'Inventory', 'Sales'];
        
        foreach ($existing_departments as $dept) {
            if (in_array($dept['name'], $my_added_departments)) {
                $duplicate_check[] = $dept;
            }
        }
        
        if (count($duplicate_check) > 0) {
            echo "<div class='warning-box'>";
            echo "<h3>⚠️ Possible Duplicates I Added:</h3>";
            echo "<p>These departments might be duplicates that I added:</p>";
            echo "<table>";
            echo "<tr><th>ID</th><th>Name</th><th>Action</th></tr>";
            foreach ($duplicate_check as $dept) {
                echo "<tr>";
                echo "<td>" . $dept['id'] . "</td>";
                echo "<td>" . htmlspecialchars($dept['name']) . "</td>";
                echo "<td><a href='remove_duplicate_department.php?id=" . $dept['id'] . "' class='btn' style='background: #f44336;'>Remove</a></td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "</div>";
        }
        
        // Check users table to see what departments are actually being used
        echo "<div class='info-box'>";
        echo "<h3>👥 Departments Used by Users:</h3>";
        
        $users_dept_sql = "SELECT department, COUNT(*) as user_count FROM users GROUP BY department ORDER BY user_count DESC";
        $users_dept_stmt = $conn->prepare($users_dept_sql);
        $users_dept_stmt->execute();
        $user_departments = $users_dept_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($user_departments) > 0) {
            echo "<table>";
            echo "<tr><th>Department</th><th>Users Count</th></tr>";
            foreach ($user_departments as $dept) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($dept['department']) . "</td>";
                echo "<td>" . $dept['user_count'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No users found with department information.</p>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error-box'>";
        echo "<h3>❌ Error checking departments</h3>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    echo "<div class='status-box'>";
    echo "<h3>🔧 What Should I Do?</h3>";
    echo "<p>Options to fix this:</p>";
    echo "<ul>";
    echo "<li><strong>Option 1:</strong> Remove duplicate departments I added</li>";
    echo "<li><strong>Option 2:</strong> Keep existing departments and update sample data to use them</li>";
    echo "<li><strong>Option 3:</strong> Merge departments if needed</li>";
    echo "</ul>";
    
    echo "<a href='cleanup_duplicate_departments.php' class='btn'>🧹 Clean Up Duplicates</a>";
    echo "<a href='update_sample_data_departments.php' class='btn'>🔄 Update Sample Data to Use Existing Departments</a>";
    echo "</div>";
    
    echo "<div class='info-box'>";
    echo "<h3>📝 My Mistake</h3>";
    echo "<p>Sorry par! I should have checked your existing departments first before adding new ones.</p>";
    echo "<p>I'll be more careful next time to check existing data before inserting new records.</p>";
    echo "</div>";
    ?>
    
    <script>
        console.log('🔍 Checking existing departments...');
    </script>
</body>
</html>
