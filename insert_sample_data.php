<?php
require_once 'app/config/config.php';

try {
    echo "<h2>Adding Comprehensive Sample Data for Reports and Dashboard</h2>";

    // 1. First, ensure all required tables exist
    echo "<h3>1. Creating Required Tables</h3>";

    // Create departments table
    $conn->exec("CREATE TABLE IF NOT EXISTS departments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    echo "✓ Departments table ready<br>";

    // Create categories table
    $conn->exec("CREATE TABLE IF NOT EXISTS categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    echo "✓ Categories table ready<br>";

    // Create products table
    $conn->exec("CREATE TABLE IF NOT EXISTS products (
        id INT AUTO_INCREMENT PRIMARY KEY,
        category_id INT,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        price DECIMAL(10,2) NOT NULL,
        stock_quantity INT NOT NULL DEFAULT 0,
        image_url VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    echo "✓ Products table ready<br>";

    // Create users table with proper structure
    $conn->exec("CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        role ENUM('admin', 'finance', 'inventory', 'logistics', 'warehouse', 'payroll', 'user') NOT NULL DEFAULT 'user',
        department_id INT,
        phone VARCHAR(20),
        address TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    echo "✓ Users table ready<br>";

    // Create procurement_orders table
    $conn->exec("CREATE TABLE IF NOT EXISTS procurement_orders (
        id INT PRIMARY KEY AUTO_INCREMENT,
        order_number VARCHAR(50) UNIQUE NOT NULL,
        requested_by INT NOT NULL,
        department_id INT NOT NULL,
        order_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        status ENUM('draft', 'pending_finance', 'pending_procurement', 'approved', 'rejected', 'completed') DEFAULT 'draft',
        total_amount DECIMAL(10,2) DEFAULT 0,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    echo "✓ Procurement orders table ready<br>";

    // Create procurement_order_items table
    $conn->exec("CREATE TABLE IF NOT EXISTS procurement_order_items (
        id INT PRIMARY KEY AUTO_INCREMENT,
        order_id INT NOT NULL,
        product_id INT,
        product_name VARCHAR(255) NOT NULL,
        brand VARCHAR(100) NOT NULL,
        quantity INT NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending'
    )");
    echo "✓ Procurement order items table ready<br>";

    // Create inventory table
    $conn->exec("CREATE TABLE IF NOT EXISTS inventory (
        id INT AUTO_INCREMENT PRIMARY KEY,
        prod_image VARCHAR(255),
        prod_name VARCHAR(100) NOT NULL,
        brand_name VARCHAR(100),
        price DECIMAL(10,2) NOT NULL,
        prod_measure VARCHAR(50),
        pack_type VARCHAR(50),
        expiry_date DATETIME,
        delivered_date DATETIME,
        country VARCHAR(100),
        batch_code VARCHAR(50),
        stocks DECIMAL(10,2) NOT NULL,
        status VARCHAR(20) NOT NULL DEFAULT 'approved',
        order_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    echo "✓ Inventory table ready<br>";

    echo "<h3>2. Inserting Sample Data</h3>";

    // 2. Insert departments
    $departments = [
        ['Administration', 'Administrative department'],
        ['Finance', 'Financial management department'],
        ['Inventory', 'Inventory management department'],
        ['Logistics', 'Logistics and delivery department'],
        ['Procurement', 'Procurement and purchasing department'],
        ['Sales', 'Sales and customer service department']
    ];

    foreach ($departments as $dept) {
        $stmt = $conn->prepare("INSERT IGNORE INTO departments (name, description) VALUES (?, ?)");
        $stmt->execute($dept);
    }
    echo "✓ Inserted " . count($departments) . " departments<br>";

    // 3. Insert categories
    $categories = [
        ['Spices', 'Various spices and seasonings'],
        ['Herbs', 'Fresh and dried herbs'],
        ['Condiments', 'Sauces and condiments'],
        ['Oils', 'Cooking oils and vinegars'],
        ['Seasonings', 'Seasoning blends and mixes']
    ];

    foreach ($categories as $cat) {
        $stmt = $conn->prepare("INSERT IGNORE INTO categories (name, description) VALUES (?, ?)");
        $stmt->execute($cat);
    }
    echo "✓ Inserted " . count($categories) . " categories<br>";

    // 4. Insert users
    $users = [
        ['admin', '<EMAIL>', password_hash('admin123', PASSWORD_DEFAULT), 'Admin', 'User', 'admin', 1, '09123456789', '123 Admin St.'],
        ['john_doe', '<EMAIL>', password_hash('password123', PASSWORD_DEFAULT), 'John', 'Doe', 'finance', 2, '09123456790', '456 Finance Ave.'],
        ['jane_smith', '<EMAIL>', password_hash('password123', PASSWORD_DEFAULT), 'Jane', 'Smith', 'inventory', 3, '09123456791', '789 Inventory Rd.'],
        ['mike_wilson', '<EMAIL>', password_hash('password123', PASSWORD_DEFAULT), 'Mike', 'Wilson', 'logistics', 4, '09123456792', '321 Logistics Blvd.'],
        ['sarah_brown', '<EMAIL>', password_hash('password123', PASSWORD_DEFAULT), 'Sarah', 'Brown', 'procurement', 5, '09123456793', '654 Procurement St.'],
        ['david_jones', '<EMAIL>', password_hash('password123', PASSWORD_DEFAULT), 'David', 'Jones', 'user', 6, '09123456794', '987 Sales Ave.']
    ];

    foreach ($users as $user) {
        $stmt = $conn->prepare("INSERT IGNORE INTO users (username, email, password, first_name, last_name, role, department_id, phone, address) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute($user);
    }
    echo "✓ Inserted " . count($users) . " users<br>";

    // 5. Insert products
    $products = [
        [1, 'Black Pepper', 'Premium black pepper from Kerala', 15.99, 100, 'black_pepper.jpg'],
        [1, 'Cinnamon Sticks', 'Ceylon cinnamon sticks', 12.50, 80, 'cinnamon.jpg'],
        [1, 'Cardamom Pods', 'Green cardamom pods', 25.00, 60, 'cardamom.jpg'],
        [1, 'Turmeric Powder', 'Organic turmeric powder', 8.99, 120, 'turmeric.jpg'],
        [1, 'Cumin Seeds', 'Whole cumin seeds', 6.75, 90, 'cumin.jpg'],
        [2, 'Dried Basil', 'Mediterranean dried basil', 4.50, 70, 'basil.jpg'],
        [2, 'Oregano', 'Italian oregano leaves', 5.25, 85, 'oregano.jpg'],
        [2, 'Thyme', 'Fresh dried thyme', 6.00, 65, 'thyme.jpg'],
        [3, 'Soy Sauce', 'Premium soy sauce', 3.99, 150, 'soy_sauce.jpg'],
        [3, 'Fish Sauce', 'Vietnamese fish sauce', 4.75, 100, 'fish_sauce.jpg'],
        [4, 'Olive Oil', 'Extra virgin olive oil', 18.99, 50, 'olive_oil.jpg'],
        [4, 'Sesame Oil', 'Pure sesame oil', 12.99, 40, 'sesame_oil.jpg'],
        [5, 'Curry Powder', 'Indian curry powder blend', 7.50, 95, 'curry_powder.jpg'],
        [5, 'Garam Masala', 'Traditional garam masala', 9.25, 75, 'garam_masala.jpg'],
        [5, 'Chinese Five Spice', 'Five spice powder blend', 8.75, 80, 'five_spice.jpg']
    ];

    foreach ($products as $product) {
        $stmt = $conn->prepare("INSERT IGNORE INTO products (category_id, name, description, price, stock_quantity, image_url) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute($product);
    }
    echo "✓ Inserted " . count($products) . " products<br>";

    // 6. Insert procurement orders with various dates and statuses
    $orders = [
        ['ORD-2024-001', 1, 2, '2024-01-15 10:30:00', 'completed', 245.50, 'Spice order for restaurant'],
        ['ORD-2024-002', 2, 3, '2024-01-20 14:15:00', 'completed', 189.75, 'Monthly inventory restock'],
        ['ORD-2024-003', 3, 4, '2024-02-05 09:45:00', 'completed', 356.25, 'Bulk order for catering'],
        ['ORD-2024-004', 4, 5, '2024-02-12 16:20:00', 'completed', 127.80, 'Special seasoning request'],
        ['ORD-2024-005', 5, 6, '2024-02-28 11:10:00', 'completed', 298.40, 'Premium spice collection'],
        ['ORD-2024-006', 1, 2, '2024-03-08 13:30:00', 'completed', 445.60, 'Large restaurant order'],
        ['ORD-2024-007', 2, 3, '2024-03-15 10:45:00', 'completed', 178.90, 'Herb and spice mix'],
        ['ORD-2024-008', 3, 4, '2024-03-22 15:20:00', 'completed', 267.35, 'Condiment restock'],
        ['ORD-2024-009', 4, 5, '2024-04-02 12:15:00', 'completed', 334.75, 'Oil and vinegar order'],
        ['ORD-2024-010', 5, 6, '2024-04-10 14:40:00', 'completed', 198.25, 'Seasoning blend order'],
        ['ORD-2024-011', 1, 2, '2024-04-18 09:30:00', 'completed', 412.80, 'Premium spice selection'],
        ['ORD-2024-012', 2, 3, '2024-04-25 16:10:00', 'completed', 289.50, 'Monthly restock'],
        ['ORD-2024-013', 3, 4, '2024-05-05 11:25:00', 'completed', 356.90, 'Bulk catering order'],
        ['ORD-2024-014', 4, 5, '2024-05-12 13:45:00', 'completed', 167.40, 'Special request order'],
        ['ORD-2024-015', 5, 6, '2024-05-20 10:20:00', 'approved', 278.65, 'Current month order'],
        ['ORD-2024-016', 1, 2, '2024-05-25 15:30:00', 'pending_finance', 189.75, 'Pending approval'],
        ['ORD-2024-017', 2, 3, '2024-05-28 12:15:00', 'draft', 234.50, 'Draft order']
    ];

    foreach ($orders as $order) {
        $stmt = $conn->prepare("INSERT IGNORE INTO procurement_orders (order_number, requested_by, department_id, created_at, status, total_amount, notes) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute($order);
    }
    echo "✓ Inserted " . count($orders) . " procurement orders<br>";

    // 7. Insert procurement order items
    $order_items = [
        // Order 1 items
        [1, 1, 'Black Pepper', 'EuroSpice', 5, 15.99, 79.95, 'approved'],
        [1, 2, 'Cinnamon Sticks', 'EuroSpice', 3, 12.50, 37.50, 'approved'],
        [1, 3, 'Cardamom Pods', 'EuroSpice', 2, 25.00, 50.00, 'approved'],

        // Order 2 items
        [2, 4, 'Turmeric Powder', 'EuroSpice', 4, 8.99, 35.96, 'approved'],
        [2, 5, 'Cumin Seeds', 'EuroSpice', 6, 6.75, 40.50, 'approved'],
        [2, 6, 'Dried Basil', 'EuroSpice', 8, 4.50, 36.00, 'approved'],

        // Order 3 items
        [3, 7, 'Oregano', 'EuroSpice', 10, 5.25, 52.50, 'approved'],
        [3, 8, 'Thyme', 'EuroSpice', 7, 6.00, 42.00, 'approved'],
        [3, 9, 'Soy Sauce', 'EuroSpice', 15, 3.99, 59.85, 'approved'],

        // Order 4 items
        [4, 10, 'Fish Sauce', 'EuroSpice', 8, 4.75, 38.00, 'approved'],
        [4, 11, 'Olive Oil', 'EuroSpice', 3, 18.99, 56.97, 'approved'],
        [4, 12, 'Sesame Oil', 'EuroSpice', 2, 12.99, 25.98, 'approved'],

        // Order 5 items
        [5, 13, 'Curry Powder', 'EuroSpice', 12, 7.50, 90.00, 'approved'],
        [5, 14, 'Garam Masala', 'EuroSpice', 8, 9.25, 74.00, 'approved'],
        [5, 15, 'Chinese Five Spice', 'EuroSpice', 6, 8.75, 52.50, 'approved'],

        // Additional items for other orders
        [6, 1, 'Black Pepper', 'EuroSpice', 8, 15.99, 127.92, 'approved'],
        [6, 3, 'Cardamom Pods', 'EuroSpice', 4, 25.00, 100.00, 'approved'],
        [6, 13, 'Curry Powder', 'EuroSpice', 15, 7.50, 112.50, 'approved'],

        [7, 6, 'Dried Basil', 'EuroSpice', 12, 4.50, 54.00, 'approved'],
        [7, 7, 'Oregano', 'EuroSpice', 9, 5.25, 47.25, 'approved'],
        [7, 8, 'Thyme', 'EuroSpice', 6, 6.00, 36.00, 'approved'],

        [8, 9, 'Soy Sauce', 'EuroSpice', 20, 3.99, 79.80, 'approved'],
        [8, 10, 'Fish Sauce', 'EuroSpice', 12, 4.75, 57.00, 'approved'],
        [8, 11, 'Olive Oil', 'EuroSpice', 4, 18.99, 75.96, 'approved'],

        [9, 11, 'Olive Oil', 'EuroSpice', 6, 18.99, 113.94, 'approved'],
        [9, 12, 'Sesame Oil', 'EuroSpice', 8, 12.99, 103.92, 'approved'],
        [9, 4, 'Turmeric Powder', 'EuroSpice', 10, 8.99, 89.90, 'approved'],

        [10, 14, 'Garam Masala', 'EuroSpice', 6, 9.25, 55.50, 'approved'],
        [10, 15, 'Chinese Five Spice', 'EuroSpice', 8, 8.75, 70.00, 'approved'],
        [10, 5, 'Cumin Seeds', 'EuroSpice', 12, 6.75, 81.00, 'approved']
    ];

    foreach ($order_items as $item) {
        $stmt = $conn->prepare("INSERT IGNORE INTO procurement_order_items (order_id, product_id, product_name, brand, quantity, unit_price, total_price, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute($item);
    }
    echo "✓ Inserted " . count($order_items) . " order items<br>";

    // 8. Insert inventory items
    $inventory_items = [
        ['black_pepper.jpg', 'Black Pepper', 'EuroSpice', 15.99, '100g', 'Jar', '2025-12-31 00:00:00', '2024-01-15 10:30:00', 'India', 'BP-2024-001', 95.00, 'approved', 1],
        ['cinnamon.jpg', 'Cinnamon Sticks', 'EuroSpice', 12.50, '50g', 'Pouch', '2025-11-30 00:00:00', '2024-01-15 10:30:00', 'Sri Lanka', 'CS-2024-001', 77.00, 'approved', 1],
        ['cardamom.jpg', 'Cardamom Pods', 'EuroSpice', 25.00, '25g', 'Jar', '2025-10-31 00:00:00', '2024-01-15 10:30:00', 'Guatemala', 'CP-2024-001', 58.00, 'approved', 1],
        ['turmeric.jpg', 'Turmeric Powder', 'EuroSpice', 8.99, '200g', 'Pouch', '2025-09-30 00:00:00', '2024-01-20 14:15:00', 'India', 'TP-2024-001', 116.00, 'approved', 2],
        ['cumin.jpg', 'Cumin Seeds', 'EuroSpice', 6.75, '100g', 'Jar', '2025-08-31 00:00:00', '2024-01-20 14:15:00', 'India', 'CU-2024-001', 84.00, 'approved', 2],
        ['basil.jpg', 'Dried Basil', 'EuroSpice', 4.50, '30g', 'Jar', '2025-07-31 00:00:00', '2024-01-20 14:15:00', 'Italy', 'DB-2024-001', 62.00, 'approved', 2],
        ['oregano.jpg', 'Oregano', 'EuroSpice', 5.25, '40g', 'Jar', '2025-06-30 00:00:00', '2024-02-05 09:45:00', 'Greece', 'OR-2024-001', 76.00, 'approved', 3],
        ['thyme.jpg', 'Thyme', 'EuroSpice', 6.00, '35g', 'Jar', '2025-05-31 00:00:00', '2024-02-05 09:45:00', 'France', 'TH-2024-001', 58.00, 'approved', 3],
        ['soy_sauce.jpg', 'Soy Sauce', 'EuroSpice', 3.99, '500ml', 'Bottle', '2026-01-31 00:00:00', '2024-02-05 09:45:00', 'Japan', 'SS-2024-001', 135.00, 'approved', 3],
        ['fish_sauce.jpg', 'Fish Sauce', 'EuroSpice', 4.75, '300ml', 'Bottle', '2026-02-28 00:00:00', '2024-02-12 16:20:00', 'Vietnam', 'FS-2024-001', 88.00, 'approved', 4],
        ['olive_oil.jpg', 'Olive Oil', 'EuroSpice', 18.99, '750ml', 'Bottle', '2025-12-31 00:00:00', '2024-02-12 16:20:00', 'Spain', 'OO-2024-001', 37.00, 'approved', 4],
        ['sesame_oil.jpg', 'Sesame Oil', 'EuroSpice', 12.99, '250ml', 'Bottle', '2025-11-30 00:00:00', '2024-02-12 16:20:00', 'China', 'SO-2024-001', 30.00, 'approved', 4],
        ['curry_powder.jpg', 'Curry Powder', 'EuroSpice', 7.50, '150g', 'Jar', '2025-10-31 00:00:00', '2024-02-28 11:10:00', 'India', 'CP-2024-002', 68.00, 'approved', 5],
        ['garam_masala.jpg', 'Garam Masala', 'EuroSpice', 9.25, '100g', 'Jar', '2025-09-30 00:00:00', '2024-02-28 11:10:00', 'India', 'GM-2024-001', 61.00, 'approved', 5],
        ['five_spice.jpg', 'Chinese Five Spice', 'EuroSpice', 8.75, '80g', 'Jar', '2025-08-31 00:00:00', '2024-02-28 11:10:00', 'China', 'FS-2024-002', 66.00, 'approved', 5]
    ];

    foreach ($inventory_items as $item) {
        $stmt = $conn->prepare("INSERT IGNORE INTO inventory (prod_image, prod_name, brand_name, price, prod_measure, pack_type, expiry_date, delivered_date, country, batch_code, stocks, status, order_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute($item);
    }
    echo "✓ Inserted " . count($inventory_items) . " inventory items<br>";

    echo "<h3>3. Data Summary</h3>";
    echo "<p>✅ All sample data has been successfully inserted!</p>";
    echo "<p>📊 Your reports and dashboard should now display meaningful data.</p>";
    echo "<p>🔗 <a href='app/modules/admin/admin_dashboard.php'>Go to Admin Dashboard</a></p>";
    echo "<p>📈 <a href='app/modules/reports/reports.php'>View Reports</a></p>";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
