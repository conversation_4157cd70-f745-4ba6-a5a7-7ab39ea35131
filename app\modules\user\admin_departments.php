<?php
session_start();
require_once '../../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header("Location: ../auth/login.php");
    exit();
}

// Get all departments from database
try {
    $stmt = $conn->prepare("SELECT * FROM departments ORDER BY name");
    $stmt->execute();
    $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $departments = [];
}

// Get users count per department
try {
    $stmt = $conn->prepare("
        SELECT department, COUNT(*) as user_count 
        FROM users 
        GROUP BY department
    ");
    $stmt->execute();
    $dept_user_counts = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
} catch (PDOException $e) {
    $dept_user_counts = [];
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Department Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #faf2e9;
        }

        .page-title {
            background-color: #f15b31;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }

        .card-header {
            background-color: #f15b31 !important;
            color: white !important;
        }

        .btn-primary {
            background-color: #f15b31;
            border-color: #f15b31;
        }

        .btn-primary:hover {
            background-color: #d14118;
            border-color: #d14118;
        }

        .table th {
            background-color: #f15b31;
            color: white;
        }
    </style>
</head>

<body>
    <div class="container mt-4">
        <h1 class="page-title">🏢 Admin - Department Management</h1>

        <!-- Back Button -->
        <div class="row mb-4">
            <div class="col-12 text-end">
                <a href="hr_reports.php" class="btn" style="background-color: #f15b31; color: white;">Back to HR Reports</a>
            </div>
        </div>

        <!-- Departments List -->
        <div class="card mb-4">
            <div class="card-header">
                <h4>All Departments</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Department Name</th>
                                <th>Description</th>
                                <th>Employee Count</th>
                                <th>Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($departments as $dept): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($dept['id']); ?></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($dept['name']); ?></strong>
                                    </td>
                                    <td><?php echo htmlspecialchars($dept['description'] ?? 'N/A'); ?></td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo $dept_user_counts[$dept['name']] ?? 0; ?> employees
                                        </span>
                                    </td>
                                    <td><?php echo isset($dept['created_at']) ? date('M d, Y', strtotime($dept['created_at'])) : 'N/A'; ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Department Statistics -->
        <div class="card mb-4">
            <div class="card-header">
                <h4>Department Statistics</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($departments as $dept): ?>
                        <div class="col-md-4 mb-3">
                            <div class="card border-left-primary">
                                <div class="card-body">
                                    <h6 class="card-title" style="color: #f15b31;"><?php echo htmlspecialchars($dept['name']); ?></h6>
                                    <p class="card-text">
                                        <strong>Employees:</strong> <?php echo $dept_user_counts[$dept['name']] ?? 0; ?><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($dept['description'] ?? 'No description'); ?></small>
                                    </p>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Back to Dashboard Button -->
        <div class="text-center mt-4 mb-4">
            <a href="../admin/admin_dashboard.php" class="btn btn-primary">Back to Dashboard</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>
