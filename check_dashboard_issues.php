<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Issues Checker</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; max-height: 300px; overflow-y: auto; }
        .status-good { color: #28a745; font-weight: bold; }
        .status-bad { color: #dc3545; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">🔍 Dashboard Issues Checker</h1>
        
        <?php
        echo "<div class='test-section'>";
        echo "<h3>1. Database Connection & Data Check</h3>";
        
        try {
            require_once 'app/config/config.php';
            echo "<p class='status-good'>✅ Database connection successful</p>";
            
            // Check each table
            $tables = [
                'users' => 'SELECT COUNT(*) as count FROM users',
                'products' => 'SELECT COUNT(*) as count FROM products',
                'inventory' => 'SELECT COUNT(*) as count FROM inventory',
                'inventory_approved' => 'SELECT COUNT(*) as count FROM inventory WHERE status = "approved"',
                'departments' => 'SELECT COUNT(*) as count FROM departments',
                'categories' => 'SELECT COUNT(*) as count FROM categories'
            ];
            
            echo "<table class='table table-sm'>";
            echo "<tr><th>Table</th><th>Count</th><th>Status</th></tr>";
            
            foreach ($tables as $name => $query) {
                try {
                    $stmt = $conn->prepare($query);
                    $stmt->execute();
                    $result = $stmt->fetch(PDO::FETCH_ASSOC);
                    $count = $result['count'];
                    
                    if ($count > 0) {
                        echo "<tr><td>$name</td><td>$count</td><td class='status-good'>✅ OK</td></tr>";
                    } else {
                        echo "<tr><td>$name</td><td>$count</td><td class='status-warning'>⚠️ Empty</td></tr>";
                    }
                } catch (Exception $e) {
                    echo "<tr><td>$name</td><td>-</td><td class='status-bad'>❌ Error: " . $e->getMessage() . "</td></tr>";
                }
            }
            echo "</table>";
            
        } catch (Exception $e) {
            echo "<p class='status-bad'>❌ Database connection failed: " . $e->getMessage() . "</p>";
        }
        echo "</div>";
        
        // Test API endpoints
        echo "<div class='test-section'>";
        echo "<h3>2. API Endpoints Test</h3>";
        
        $endpoints = [
            'app/modules/get_inventory_data.php' => 'Inventory Data API',
            'app/modules/get_low_stock.php' => 'Low Stock API',
            'includes/get_inventory_data.php' => 'Includes Inventory API',
            'includes/get_low_stock.php' => 'Includes Low Stock API'
        ];
        
        foreach ($endpoints as $endpoint => $name) {
            echo "<h5>$name ($endpoint)</h5>";
            
            $url = 'http://localhost/finance/' . $endpoint;
            $context = stream_context_create([
                'http' => [
                    'timeout' => 5,
                    'ignore_errors' => true
                ]
            ]);
            
            $response = @file_get_contents($url, false, $context);
            
            if ($response === false) {
                echo "<p class='status-bad'>❌ Failed to fetch from: $endpoint</p>";
                echo "<p>URL tried: $url</p>";
            } else {
                $data = json_decode($response, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    if (count($data) > 0) {
                        echo "<p class='status-good'>✅ Success! Retrieved " . count($data) . " records</p>";
                        echo "<pre>" . json_encode(array_slice($data, 0, 2), JSON_PRETTY_PRINT) . "</pre>";
                    } else {
                        echo "<p class='status-warning'>⚠️ API working but returned empty data</p>";
                    }
                } else {
                    echo "<p class='status-bad'>❌ JSON Error: " . json_last_error_msg() . "</p>";
                    echo "<p>Raw response (first 500 chars):</p>";
                    echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
                }
            }
        }
        echo "</div>";
        
        // Test admin dashboard function
        echo "<div class='test-section'>";
        echo "<h3>3. Admin Dashboard Function Test</h3>";
        
        if (isset($conn)) {
            function getModuleStats($conn, $module) {
                $stats = [];
                try {
                    switch ($module) {
                        case 'users':
                            $query = "SELECT COUNT(*) as total FROM users";
                            $stmt = $conn->prepare($query);
                            $stmt->execute();
                            $result = $stmt->fetch(PDO::FETCH_ASSOC);
                            $stats['total_users'] = $result['total'] ?? 0;
                            break;
                        case 'products':
                            $query = "SELECT COUNT(*) as total FROM products";
                            $stmt = $conn->prepare($query);
                            $stmt->execute();
                            $result = $stmt->fetch(PDO::FETCH_ASSOC);
                            $stats['total_products'] = $result['total'] ?? 0;
                            break;
                        case 'inventory':
                            $query = "SELECT COUNT(*) as total FROM inventory WHERE status = 'approved'";
                            $stmt = $conn->prepare($query);
                            $stmt->execute();
                            $result = $stmt->fetch(PDO::FETCH_ASSOC);
                            $stats['total_items'] = $result['total'] ?? 0;
                            break;
                    }
                } catch (Exception $e) {
                    echo "<p class='status-bad'>Error in $module: " . $e->getMessage() . "</p>";
                    switch ($module) {
                        case 'users': $stats['total_users'] = 0; break;
                        case 'products': $stats['total_products'] = 0; break;
                        case 'inventory': $stats['total_items'] = 0; break;
                    }
                }
                return $stats;
            }
            
            $moduleStats = [
                'users' => getModuleStats($conn, 'users'),
                'products' => getModuleStats($conn, 'products'),
                'inventory' => getModuleStats($conn, 'inventory')
            ];
            
            echo "<div class='row'>";
            echo "<div class='col-md-4'>";
            echo "<div class='card text-center' style='background: linear-gradient(45deg, #4b6cb7, #182848); color: white;'>";
            echo "<div class='card-body'>";
            echo "<h5>Total Users</h5>";
            echo "<h2>" . ($moduleStats['users']['total_users'] ?? 0) . "</h2>";
            echo "</div></div></div>";
            
            echo "<div class='col-md-4'>";
            echo "<div class='card text-center' style='background: linear-gradient(45deg, #4b6cb7, #182848); color: white;'>";
            echo "<div class='card-body'>";
            echo "<h5>Total Products</h5>";
            echo "<h2>" . ($moduleStats['products']['total_products'] ?? 0) . "</h2>";
            echo "</div></div></div>";
            
            echo "<div class='col-md-4'>";
            echo "<div class='card text-center' style='background: linear-gradient(45deg, #4b6cb7, #182848); color: white;'>";
            echo "<div class='card-body'>";
            echo "<h5>Inventory Items</h5>";
            echo "<h2>" . ($moduleStats['inventory']['total_items'] ?? 0) . "</h2>";
            echo "</div></div></div>";
            echo "</div>";
            
            if ($moduleStats['users']['total_users'] == 0 && $moduleStats['products']['total_products'] == 0 && $moduleStats['inventory']['total_items'] == 0) {
                echo "<div class='alert alert-danger mt-3'>";
                echo "<h5>❌ Problem Found: All dashboard cards showing 0</h5>";
                echo "<p>This means either:</p>";
                echo "<ul>";
                echo "<li>No data in database tables</li>";
                echo "<li>Database connection issues</li>";
                echo "<li>Query execution problems</li>";
                echo "</ul>";
                echo "</div>";
            } else {
                echo "<div class='alert alert-success mt-3'>";
                echo "<h5>✅ Dashboard cards should be working!</h5>";
                echo "</div>";
            }
        }
        echo "</div>";
        ?>
        
        <div class="test-section">
            <h3>4. JavaScript Chart Test</h3>
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Inventory Chart Test</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="testChart" style="height: 300px;"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Test Results</h5>
                        </div>
                        <div class="card-body">
                            <div id="chartTestResults">
                                <p>Testing chart functionality...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>5. Quick Actions</h3>
            <div class="row">
                <div class="col-md-3">
                    <a href="app/modules/admin/admin_dashboard.php" class="btn btn-primary w-100 mb-2">Check Admin Dashboard</a>
                </div>
                <div class="col-md-3">
                    <a href="app/modules/inventory/inventory_dashboard.php" class="btn btn-success w-100 mb-2">Check Inventory Dashboard</a>
                </div>
                <div class="col-md-3">
                    <a href="add_sample_data_simple.php" class="btn btn-warning w-100 mb-2">Re-run Data Insert</a>
                </div>
                <div class="col-md-3">
                    <a href="verify_data.php" class="btn btn-info w-100 mb-2">Verify Data</a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
    <script>
        async function testChart() {
            const resultsDiv = document.getElementById('chartTestResults');
            const ctx = document.getElementById('testChart').getContext('2d');
            
            try {
                resultsDiv.innerHTML += '<p>🔄 Testing API fetch...</p>';
                
                const response = await fetch('app/modules/get_inventory_data.php');
                resultsDiv.innerHTML += '<p>📡 API Response: ' + response.status + '</p>';
                
                if (!response.ok) {
                    throw new Error('API response not OK: ' + response.status);
                }
                
                const data = await response.json();
                resultsDiv.innerHTML += '<p>📊 Data received: ' + data.length + ' records</p>';
                
                if (data.length === 0) {
                    resultsDiv.innerHTML += '<p class="status-warning">⚠️ No data to chart</p>';
                    return;
                }
                
                // Create test chart
                const brands = data.map(item => item.brand_name);
                const stocks = data.map(item => item.total_stock);
                
                new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: brands,
                        datasets: [{
                            data: stocks,
                            backgroundColor: ['#4b6cb7', '#182848', '#2c3e50', '#3498db', '#2980b9']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
                
                resultsDiv.innerHTML += '<p class="status-good">✅ Chart created successfully!</p>';
                
            } catch (error) {
                resultsDiv.innerHTML += '<p class="status-bad">❌ Chart test failed: ' + error.message + '</p>';
            }
        }
        
        // Run chart test
        testChart();
    </script>
</body>
</html>
