<?php
// Verification script to check if data was inserted successfully
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "finance";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<h2>Data Verification Report</h2>";
echo "<style>
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f15b31; color: white; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
</style>";

try {
    // Check departments
    $result = $conn->query("SELECT COUNT(*) as count FROM departments");
    $count = $result->fetch_assoc()['count'];
    echo "<p class='success'>✓ Departments: $count records</p>";
    
    // Check categories
    $result = $conn->query("SELECT COUNT(*) as count FROM categories");
    $count = $result->fetch_assoc()['count'];
    echo "<p class='success'>✓ Categories: $count records</p>";
    
    // Check users
    $result = $conn->query("SELECT COUNT(*) as count FROM users");
    $count = $result->fetch_assoc()['count'];
    echo "<p class='success'>✓ Users: $count records</p>";
    
    // Check products
    $result = $conn->query("SELECT COUNT(*) as count FROM products");
    $count = $result->fetch_assoc()['count'];
    echo "<p class='success'>✓ Products: $count records</p>";
    
    // Check procurement orders
    $result = $conn->query("SELECT COUNT(*) as count FROM procurement_orders");
    $count = $result->fetch_assoc()['count'];
    echo "<p class='success'>✓ Procurement Orders: $count records</p>";
    
    // Check procurement order items
    $result = $conn->query("SELECT COUNT(*) as count FROM procurement_order_items");
    $count = $result->fetch_assoc()['count'];
    echo "<p class='success'>✓ Order Items: $count records</p>";
    
    // Check inventory
    $result = $conn->query("SELECT COUNT(*) as count FROM inventory");
    $count = $result->fetch_assoc()['count'];
    echo "<p class='success'>✓ Inventory: $count records</p>";
    
    echo "<h3>Sample Data Preview</h3>";
    
    // Show sample procurement orders
    echo "<h4>Recent Procurement Orders</h4>";
    $result = $conn->query("SELECT order_number, status, total_amount, created_at FROM procurement_orders ORDER BY created_at DESC LIMIT 5");
    if ($result->num_rows > 0) {
        echo "<table>";
        echo "<tr><th>Order Number</th><th>Status</th><th>Total Amount</th><th>Date</th></tr>";
        while($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['order_number'] . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "<td>$" . number_format($row['total_amount'], 2) . "</td>";
            echo "<td>" . $row['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Show sample inventory
    echo "<h4>Sample Inventory Items</h4>";
    $result = $conn->query("SELECT prod_name, brand_name, price, stocks, status FROM inventory LIMIT 5");
    if ($result->num_rows > 0) {
        echo "<table>";
        echo "<tr><th>Product</th><th>Brand</th><th>Price</th><th>Stock</th><th>Status</th></tr>";
        while($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['prod_name'] . "</td>";
            echo "<td>" . $row['brand_name'] . "</td>";
            echo "<td>$" . number_format($row['price'], 2) . "</td>";
            echo "<td>" . $row['stocks'] . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Show sales summary
    echo "<h4>Sales Summary</h4>";
    $result = $conn->query("SELECT 
        COUNT(*) as total_orders,
        SUM(total_amount) as total_sales,
        AVG(total_amount) as avg_order_value
        FROM procurement_orders 
        WHERE status = 'completed'");
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        echo "<table>";
        echo "<tr><th>Metric</th><th>Value</th></tr>";
        echo "<tr><td>Total Completed Orders</td><td>" . $row['total_orders'] . "</td></tr>";
        echo "<tr><td>Total Sales</td><td>$" . number_format($row['total_sales'], 2) . "</td></tr>";
        echo "<tr><td>Average Order Value</td><td>$" . number_format($row['avg_order_value'], 2) . "</td></tr>";
        echo "</table>";
    }
    
    echo "<h3>Quick Links</h3>";
    echo "<p>🔗 <a href='app/modules/admin/admin_dashboard.php' style='color: #f15b31; text-decoration: none; font-weight: bold;'>Go to Admin Dashboard</a></p>";
    echo "<p>📈 <a href='app/modules/reports/reports.php' style='color: #f15b31; text-decoration: none; font-weight: bold;'>View Reports</a></p>";
    echo "<p>📊 <a href='app/modules/inventory/inventory_dashboard.php' style='color: #f15b31; text-decoration: none; font-weight: bold;'>Inventory Dashboard</a></p>";
    
} catch (Exception $e) {
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>";
}

$conn->close();
?>
