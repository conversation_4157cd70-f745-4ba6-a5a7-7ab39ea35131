<?php
session_start();
require_once '../../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../../auth/login.php");
    exit();
}

// Get payroll records with user info
try {
    $stmt = $conn->prepare("
        SELECT p.*,
               u.username,
               u.email,
               u.role,
               u.department
        FROM payroll p
        LEFT JOIN users u ON p.user_id = u.id
        ORDER BY p.payment_date DESC
    ");
    $stmt->execute();
    $payroll_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Add employee name based on available data
    foreach ($payroll_records as &$record) {
        if (!empty($record['username'])) {
            $record['employee_name'] = $record['username'];
        } else {
            $record['employee_name'] = 'User ID: ' . $record['user_id'];
        }
    }

} catch (PDOException $e) {
    $payroll_records = [];
}

// Calculate totals
$total_gross_pay = 0;
$total_net_pay = 0;
$total_deductions = 0;
$processed_count = 0;
$pending_count = 0;

foreach ($payroll_records as $record) {
    $total_gross_pay += $record['gross_pay'];
    $total_net_pay += $record['net_pay'];
    $total_deductions += $record['deductions'];

    if ($record['status'] === 'processed') {
        $processed_count++;
    } else {
        $pending_count++;
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payroll Management - Simple</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stats-card h6 {
            color: #666;
            margin-bottom: 10px;
        }

        .stats-card h3 {
            color: #f15b31;
            margin: 0;
        }
    </style>
</head>

<body style="background-color: #f8f9fa;">
    <div class="container mt-4">
        <!-- Page Title -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="text-center p-4 rounded" style="background-color: #f15b31; color: white;">
                    <h2><i class="fas fa-money-bill-wave"></i> Payroll Management - Simple Version</h2>
                    <p class="mb-0">View and manage employee payroll records</p>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <h6>Total Records</h6>
                    <h3><?php echo count($payroll_records); ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h6>Total Gross Pay</h6>
                    <h3>₱<?php echo number_format($total_gross_pay, 2); ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h6>Total Net Pay</h6>
                    <h3>₱<?php echo number_format($total_net_pay, 2); ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h6>Processed/Pending</h6>
                    <h3><?php echo $processed_count; ?>/<?php echo $pending_count; ?></h3>
                </div>
            </div>
        </div>

        <!-- Payroll Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header" style="background-color: #f15b31; color: white;">
                        <h5><i class="fas fa-table"></i> Payroll Records</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead style="background-color: #f15b31; color: white;">
                                    <tr>
                                        <th>ID</th>
                                        <th>User ID</th>
                                        <th>Pay Rate</th>
                                        <th>Hours Worked</th>
                                        <th>Gross Pay</th>
                                        <th>Deductions</th>
                                        <th>Net Pay</th>
                                        <th>Pay Period</th>
                                        <th>Payment Date</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (count($payroll_records) > 0): ?>
                                        <?php foreach ($payroll_records as $record): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($record['id']); ?></td>
                                                <td><strong>User <?php echo htmlspecialchars($record['user_id']); ?></strong></td>
                                                <td>₱<?php echo number_format($record['pay_rate'], 2); ?></td>
                                                <td><?php echo number_format($record['hours_worked'], 1); ?> hrs</td>
                                                <td>₱<?php echo number_format($record['gross_pay'], 2); ?></td>
                                                <td>₱<?php echo number_format($record['deductions'], 2); ?></td>
                                                <td><strong>₱<?php echo number_format($record['net_pay'], 2); ?></strong></td>
                                                <td><?php echo htmlspecialchars($record['pay_period']); ?></td>
                                                <td><?php echo date('M d, Y', strtotime($record['payment_date'])); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $record['status'] == 'processed' ? 'success' : 'warning'; ?>">
                                                        <?php echo ucfirst($record['status']); ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="10" class="text-center">
                                                <div class="alert alert-info">
                                                    <i class="fas fa-info-circle"></i> No payroll records found.
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back Button -->
        <div class="text-center mt-4 mb-4">
            <a href="../admin/admin_dashboard.php" class="btn" style="background-color: #f15b31; color: white;">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>