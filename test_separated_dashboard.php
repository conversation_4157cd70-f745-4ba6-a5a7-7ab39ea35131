<!DOCTYPE html>
<html>
<head>
    <title>Test Separated Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-box { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        .error { background: #ffebee; border-color: #f44336; }
        .warning { background: #fff3e0; border-color: #ff9800; }
        .btn { background: #f15b31; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
    </style>
</head>
<body>
    <h1>🔧 Test Separated Dashboard</h1>
    
    <?php
    require_once 'app/config/config.php';
    
    echo "<div class='test-box'>";
    echo "<h3>1. Database Connection</h3>";
    try {
        $stmt = $conn->prepare("SELECT 1");
        $stmt->execute();
        echo "<p class='success'>✅ Database connected successfully</p>";
    } catch (Exception $e) {
        echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    echo "<div class='test-box'>";
    echo "<h3>2. Sales Data Test</h3>";
    try {
        $sales_sql = "SELECT
            COUNT(*) as total_orders,
            SUM(total_amount) as total_sales,
            AVG(total_amount) as average_order_value
          FROM procurement_orders
          WHERE status = 'completed'";

        $sales_stmt = $conn->prepare($sales_sql);
        $sales_stmt->execute();
        $sales_result = $sales_stmt->fetch(PDO::FETCH_ASSOC);
        
        $total_sales = $sales_result['total_sales'] ?? 0;
        $total_orders = $sales_result['total_orders'] ?? 0;
        $avg_order_value = $sales_result['average_order_value'] ?? 0;
        
        echo "<p><strong>Sales Results:</strong></p>";
        echo "<p>📊 Total Orders: " . number_format($total_orders) . "</p>";
        echo "<p>💰 Total Sales: $" . number_format($total_sales, 2) . "</p>";
        echo "<p>📈 Average Order Value: $" . number_format($avg_order_value, 2) . "</p>";
        
        if ($total_orders > 0) {
            echo "<p class='success'>✅ Sales data is working!</p>";
        } else {
            echo "<p class='warning'>⚠️ No sales data found. Need to add sample data.</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Sales query error: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    echo "<div class='test-box'>";
    echo "<h3>3. Inventory Data Test</h3>";
    try {
        $inv_sql = "SELECT brand_name, COUNT(*) as products, SUM(stocks) as total_stock FROM inventory WHERE status = 'approved' GROUP BY brand_name";
        $inv_stmt = $conn->prepare($inv_sql);
        $inv_stmt->execute();
        $inv_results = $inv_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($inv_results) > 0) {
            echo "<p class='success'>✅ Inventory data found:</p>";
            foreach ($inv_results as $row) {
                echo "<p>🏷️ " . $row['brand_name'] . ": " . $row['products'] . " products, " . $row['total_stock'] . " stock</p>";
            }
        } else {
            echo "<p class='warning'>⚠️ No inventory data found. Need to add sample data.</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Inventory query error: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    echo "<div class='test-box'>";
    echo "<h3>4. API Test</h3>";
    $api_url = 'http://localhost/finance/app/modules/get_inventory_data.php';
    $response = @file_get_contents($api_url);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($data)) {
            echo "<p class='success'>✅ API working: " . count($data) . " records</p>";
            if (count($data) > 0) {
                echo "<p>Sample data: " . $data[0]['brand_name'] . " - " . $data[0]['total_stock'] . " stock</p>";
            }
        } else {
            echo "<p class='error'>❌ API JSON error or empty response</p>";
            echo "<pre>" . htmlspecialchars(substr($response, 0, 200)) . "</pre>";
        }
    } else {
        echo "<p class='error'>❌ API not accessible</p>";
    }
    echo "</div>";
    
    echo "<div class='test-box'>";
    echo "<h3>5. Dashboard Status</h3>";
    
    if ($total_orders > 0 && count($inv_results) > 0) {
        echo "<p class='success'>🎉 Everything looks good! Dashboard should work now.</p>";
        echo "<p><strong>Expected Dashboard Values:</strong></p>";
        echo "<p>• Total Sales: $" . number_format($total_sales, 2) . "</p>";
        echo "<p>• Total Orders: " . number_format($total_orders) . "</p>";
        echo "<p>• Average Order Value: $" . number_format($avg_order_value, 2) . "</p>";
        echo "<p>• Inventory Brands: " . count($inv_results) . "</p>";
    } else {
        echo "<p class='warning'>⚠️ Need to add sample data first</p>";
    }
    echo "</div>";
    ?>
    
    <div class="test-box">
        <h3>6. Action Links</h3>
        
        <?php if ($total_orders == 0 || count($inv_results) == 0): ?>
            <a href="add_sample_data_simple.php" class="btn">🔧 Add Sample Data First</a>
        <?php endif; ?>
        
        <a href="app/modules/inventory/inventory_dashboard.php" class="btn">📊 Test Dashboard</a>
        <a href="simple_debug.php" class="btn">🔍 Simple Debug</a>
        <a href="test_sales_only.php" class="btn">💰 Test Sales Only</a>
    </div>
    
    <div class="test-box">
        <h3>7. What I Fixed</h3>
        <p>✅ <strong>Separated all code</strong> - No more includes causing conflicts</p>
        <p>✅ <strong>Direct SQL queries</strong> - Sales data fetched directly in dashboard</p>
        <p>✅ <strong>Simplified JavaScript</strong> - Inventory chart code directly in file</p>
        <p>✅ <strong>Better error handling</strong> - Console logs and error messages</p>
        <p>✅ <strong>Orange theme</strong> - All cards and charts use your color scheme</p>
        
        <p><strong>Now the dashboard file is completely self-contained!</strong></p>
    </div>
    
    <script>
        console.log('🔍 Test page loaded');
        console.log('Dashboard should now work without include conflicts');
    </script>
</body>
</html>
