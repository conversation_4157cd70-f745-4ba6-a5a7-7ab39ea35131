<!DOCTYPE html>
<html>
<head>
    <title>Test Separated Reports</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-box { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        .error { background: #ffebee; border-color: #f44336; }
        .warning { background: #fff3e0; border-color: #ff9800; }
        .btn { background: #f15b31; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
        .report-preview { display: flex; gap: 15px; flex-wrap: wrap; margin: 15px 0; }
        .report-card { background: linear-gradient(45deg, #f15b31, #d14426); color: white; padding: 20px; border-radius: 10px; text-align: center; min-width: 200px; text-decoration: none; }
        .report-card:hover { color: white; text-decoration: none; transform: translateY(-2px); }
    </style>
</head>
<body>
    <h1>🔧 Test Separated Reports</h1>
    
    <?php
    require_once 'app/config/config.php';
    
    echo "<div class='test-box'>";
    echo "<h3>1. Database Connection</h3>";
    try {
        $stmt = $conn->prepare("SELECT 1");
        $stmt->execute();
        echo "<p class='success'>✅ Database connected successfully</p>";
    } catch (Exception $e) {
        echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    echo "<div class='test-box'>";
    echo "<h3>2. Reports Files Check</h3>";
    
    $report_files = [
        'app/modules/reports/reports.php' => 'Main Reports Dashboard',
        'app/modules/reports/sales_report.php' => 'Sales Report',
        'app/modules/reports/inventory_report.php' => 'Inventory Report',
        'app/modules/reports/finance_report.php' => 'Finance Report'
    ];
    
    $all_files_exist = true;
    foreach ($report_files as $file => $description) {
        if (file_exists($file)) {
            echo "<p class='success'>✅ $description - File exists</p>";
        } else {
            echo "<p class='error'>❌ $description - File missing</p>";
            $all_files_exist = false;
        }
    }
    
    if ($all_files_exist) {
        echo "<p class='success'>🎉 All report files are properly separated!</p>";
    }
    echo "</div>";
    
    echo "<div class='test-box'>";
    echo "<h3>3. Data Availability Check</h3>";
    
    try {
        // Check sales data
        $sales_sql = "SELECT COUNT(*) as orders, SUM(total_amount) as total FROM procurement_orders WHERE status = 'completed'";
        $sales_stmt = $conn->prepare($sales_sql);
        $sales_stmt->execute();
        $sales_stats = $sales_stmt->fetch(PDO::FETCH_ASSOC);
        
        // Check inventory data
        $inventory_sql = "SELECT COUNT(*) as items, SUM(stocks) as stock FROM inventory WHERE status = 'approved'";
        $inventory_stmt = $conn->prepare($inventory_sql);
        $inventory_stmt->execute();
        $inventory_stats = $inventory_stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<p>📊 Sales Orders: " . number_format($sales_stats['orders']) . "</p>";
        echo "<p>💰 Total Sales: $" . number_format($sales_stats['total'], 2) . "</p>";
        echo "<p>📦 Inventory Items: " . number_format($inventory_stats['items']) . "</p>";
        echo "<p>📋 Total Stock: " . number_format($inventory_stats['stock']) . "</p>";
        
        if ($sales_stats['orders'] > 0 && $inventory_stats['items'] > 0) {
            echo "<p class='success'>✅ Data is available for all reports!</p>";
        } else {
            echo "<p class='warning'>⚠️ Some data is missing. Reports may show empty tables.</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Data check error: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    echo "<div class='test-box'>";
    echo "<h3>4. Reports Preview</h3>";
    echo "<p>Click on each report card to test the separated reports:</p>";
    
    echo "<div class='report-preview'>";
    echo "<a href='app/modules/reports/reports.php' class='report-card'>";
    echo "<h5>📊 Main Dashboard</h5>";
    echo "<p>Reports overview with navigation</p>";
    echo "</a>";
    
    echo "<a href='app/modules/reports/sales_report.php' class='report-card'>";
    echo "<h5>📈 Sales Report</h5>";
    echo "<p>Detailed sales analytics</p>";
    echo "</a>";
    
    echo "<a href='app/modules/reports/inventory_report.php' class='report-card'>";
    echo "<h5>📦 Inventory Report</h5>";
    echo "<p>Stock levels and products</p>";
    echo "</a>";
    
    echo "<a href='app/modules/reports/finance_report.php' class='report-card'>";
    echo "<h5>💰 Finance Report</h5>";
    echo "<p>Expenses and budgets</p>";
    echo "</a>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='test-box'>";
    echo "<h3>5. What I Fixed</h3>";
    echo "<p>✅ <strong>Separated Reports</strong> - No more single huge file with everything</p>";
    echo "<p>✅ <strong>Individual Report Files</strong> - Each report is now its own file</p>";
    echo "<p>✅ <strong>Clean Navigation</strong> - Main reports page with cards to navigate</p>";
    echo "<p>✅ <strong>No Include Conflicts</strong> - Each file is self-contained</p>";
    echo "<p>✅ <strong>Orange Theme</strong> - Consistent styling across all reports</p>";
    echo "<p>✅ <strong>Print Functionality</strong> - Each report can be printed separately</p>";
    
    echo "<p><strong>Now you have 4 separate files:</strong></p>";
    echo "<ul>";
    echo "<li><strong>reports.php</strong> - Main dashboard with navigation cards</li>";
    echo "<li><strong>sales_report.php</strong> - Sales data and transactions</li>";
    echo "<li><strong>inventory_report.php</strong> - Inventory levels and products</li>";
    echo "<li><strong>finance_report.php</strong> - Financial data and expenses</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='test-box'>";
    echo "<h3>6. Benefits of Separation</h3>";
    echo "<p>✅ <strong>Faster Loading</strong> - Each page loads only what it needs</p>";
    echo "<p>✅ <strong>Easier Maintenance</strong> - Edit one report without affecting others</p>";
    echo "<p>✅ <strong>Better User Experience</strong> - Users can bookmark specific reports</p>";
    echo "<p>✅ <strong>No Conflicts</strong> - No more variable scope or include issues</p>";
    echo "<p>✅ <strong>Cleaner Code</strong> - Each file has a single responsibility</p>";
    echo "<p>✅ <strong>Better Performance</strong> - No unnecessary data loading</p>";
    echo "</div>";
    ?>
    
    <div class="test-box">
        <h3>7. Test Links</h3>
        
        <?php if (!$all_files_exist || $sales_stats['orders'] == 0): ?>
            <a href="add_sample_data_simple.php" class="btn">🔧 Add Sample Data First</a>
        <?php endif; ?>
        
        <a href="app/modules/reports/reports.php" class="btn">📊 Test Main Reports</a>
        <a href="app/modules/admin/admin_dashboard.php" class="btn">🏠 Admin Dashboard</a>
        <a href="app/modules/inventory/inventory_dashboard.php" class="btn">📦 Inventory Dashboard</a>
    </div>
    
    <div class="test-box">
        <h3>8. Navigation Flow</h3>
        <p><strong>User Journey:</strong></p>
        <p>1. Admin Dashboard → Reports link</p>
        <p>2. Reports Dashboard → Choose specific report</p>
        <p>3. Specific Report → View data, print if needed</p>
        <p>4. Back to Dashboard button on each page</p>
        
        <p><strong>Each report is now completely independent!</strong></p>
    </div>
    
    <script>
        console.log('🔍 Separated reports test page loaded');
        console.log('All reports are now individual files - no more conflicts!');
    </script>
</body>
</html>
