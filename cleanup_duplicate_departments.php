<!DOCTYPE html>
<html>
<head>
    <title>Cleanup Duplicate Departments</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #faf2e9; }
        .status-box { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        .error { background: #ffebee; border-color: #f44336; }
        .warning { background: #fff3e0; border-color: #ff9800; }
        .info { background: #e3f2fd; border-color: #2196f3; }
        .btn { background: #f15b31; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
        .page-title { background-color: #f15b31; color: white; padding: 20px; border-radius: 5px; text-align: center; margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f15b31; color: white; }
    </style>
</head>
<body>
    <h1 class="page-title">🧹 Cleanup Duplicate Departments</h1>
    
    <?php
    require_once 'app/config/config.php';
    
    echo "<div class='warning-box'>";
    echo "<h3>🔧 Cleaning Up Duplicate Departments</h3>";
    echo "<p>I'll remove the duplicate departments I added and update the sample data to use your existing departments...</p>";
    echo "</div>";
    
    $success_count = 0;
    $error_count = 0;
    
    try {
        $conn->beginTransaction();
        
        // First, get your existing departments (the original ones)
        echo "<div class='info-box'>";
        echo "<h3>📋 Step 1: Identifying Your Original Departments</h3>";
        
        $original_dept_sql = "SELECT * FROM departments ORDER BY id";
        $original_dept_stmt = $conn->prepare($original_dept_sql);
        $original_dept_stmt->execute();
        $all_departments = $original_dept_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>Found " . count($all_departments) . " departments total:</p>";
        echo "<table>";
        echo "<tr><th>ID</th><th>Name</th><th>Description</th><th>Status</th></tr>";
        
        $my_added_departments = ['Finance', 'Procurement', 'Logistics', 'Inventory', 'Sales'];
        $original_departments = [];
        $duplicate_departments = [];
        
        foreach ($all_departments as $dept) {
            if (in_array($dept['name'], $my_added_departments)) {
                // Check if this looks like one I added (recent creation, generic description)
                if (strpos($dept['description'] ?? '', 'Department') !== false) {
                    $duplicate_departments[] = $dept;
                    echo "<tr style='background: #ffebee;'>";
                    echo "<td>" . $dept['id'] . "</td>";
                    echo "<td>" . htmlspecialchars($dept['name']) . "</td>";
                    echo "<td>" . htmlspecialchars($dept['description'] ?? 'N/A') . "</td>";
                    echo "<td>🗑️ Will Remove (Duplicate)</td>";
                    echo "</tr>";
                } else {
                    $original_departments[] = $dept;
                    echo "<tr style='background: #e8f5e8;'>";
                    echo "<td>" . $dept['id'] . "</td>";
                    echo "<td>" . htmlspecialchars($dept['name']) . "</td>";
                    echo "<td>" . htmlspecialchars($dept['description'] ?? 'N/A') . "</td>";
                    echo "<td>✅ Keep (Original)</td>";
                    echo "</tr>";
                }
            } else {
                $original_departments[] = $dept;
                echo "<tr style='background: #e8f5e8;'>";
                echo "<td>" . $dept['id'] . "</td>";
                echo "<td>" . htmlspecialchars($dept['name']) . "</td>";
                echo "<td>" . htmlspecialchars($dept['description'] ?? 'N/A') . "</td>";
                echo "<td>✅ Keep (Original)</td>";
                echo "</tr>";
            }
        }
        echo "</table>";
        echo "</div>";
        
        // Remove duplicate departments
        if (count($duplicate_departments) > 0) {
            echo "<div class='warning-box'>";
            echo "<h3>🗑️ Step 2: Removing Duplicate Departments</h3>";
            
            foreach ($duplicate_departments as $dept) {
                try {
                    $delete_sql = "DELETE FROM departments WHERE id = ?";
                    $delete_stmt = $conn->prepare($delete_sql);
                    $delete_stmt->execute([$dept['id']]);
                    
                    echo "<p style='color: green;'>✅ Removed duplicate: " . htmlspecialchars($dept['name']) . " (ID: " . $dept['id'] . ")</p>";
                    $success_count++;
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Error removing " . htmlspecialchars($dept['name']) . ": " . $e->getMessage() . "</p>";
                    $error_count++;
                }
            }
            echo "</div>";
        } else {
            echo "<div class='info-box'>";
            echo "<h3>ℹ️ No Duplicate Departments Found</h3>";
            echo "<p>No duplicate departments to remove.</p>";
            echo "</div>";
        }
        
        // Update sample users to use existing departments
        echo "<div class='info-box'>";
        echo "<h3>🔄 Step 3: Updating Sample Users to Use Your Existing Departments</h3>";
        
        // Get the names of your existing departments
        $existing_dept_names = array_column($original_departments, 'name');
        
        if (count($existing_dept_names) > 0) {
            echo "<p>Your existing departments: " . implode(', ', $existing_dept_names) . "</p>";
            
            // Update sample users to use existing departments
            $sample_users = [
                '<EMAIL>',
                '<EMAIL>', 
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'
            ];
            
            foreach ($sample_users as $index => $email) {
                if (isset($existing_dept_names[$index % count($existing_dept_names)])) {
                    $dept_name = $existing_dept_names[$index % count($existing_dept_names)];
                    
                    try {
                        $update_user_sql = "UPDATE users SET department = ? WHERE email = ?";
                        $update_user_stmt = $conn->prepare($update_user_sql);
                        $update_user_stmt->execute([$dept_name, $email]);
                        
                        if ($update_user_stmt->rowCount() > 0) {
                            echo "<p style='color: green;'>✅ Updated user $email to department: $dept_name</p>";
                            $success_count++;
                        }
                    } catch (Exception $e) {
                        echo "<p style='color: red;'>❌ Error updating user $email: " . $e->getMessage() . "</p>";
                        $error_count++;
                    }
                }
            }
        } else {
            echo "<p style='color: orange;'>⚠️ No existing departments found to assign to users.</p>";
        }
        echo "</div>";
        
        $conn->commit();
        
        echo "<div class='success-box'>";
        echo "<h3>🎉 Cleanup Complete!</h3>";
        echo "<p><strong>Results:</strong></p>";
        echo "<ul>";
        echo "<li>✅ <strong>Successful operations:</strong> $success_count</li>";
        echo "<li>❌ <strong>Errors:</strong> $error_count</li>";
        echo "</ul>";
        echo "</div>";
        
    } catch (Exception $e) {
        $conn->rollback();
        echo "<div class='error-box'>";
        echo "<h3>❌ Cleanup Failed</h3>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    echo "<div class='info-box'>";
    echo "<h3>📋 What Was Done:</h3>";
    echo "<ul>";
    echo "<li>✅ Removed duplicate departments I accidentally added</li>";
    echo "<li>✅ Kept your original departments intact</li>";
    echo "<li>✅ Updated sample users to use your existing departments</li>";
    echo "<li>✅ Sample orders and products remain unchanged</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='status-box'>";
    echo "<h3>🎯 Test Your Reports Now</h3>";
    echo "<p>The reports should now use your original departments:</p>";
    echo "<a href='app/modules/reports/reports.php' class='btn'>📊 Test Reports</a>";
    echo "<a href='check_existing_departments.php' class='btn'>🔍 Check Departments Again</a>";
    echo "<a href='debug_reports_tables.php' class='btn'>🔍 Debug Database</a>";
    echo "</div>";
    
    echo "<div class='success-box'>";
    echo "<h3>🙏 Sorry About That!</h3>";
    echo "<p>Sorry par for not checking your existing departments first!</p>";
    echo "<p>I've cleaned up the duplicates and now the sample data uses your original departments.</p>";
    echo "<p>Next time I'll always check existing data before adding new records.</p>";
    echo "</div>";
    ?>
    
    <script>
        console.log('🧹 Cleanup duplicate departments complete');
    </script>
</body>
</html>
