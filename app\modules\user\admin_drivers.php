<?php
session_start();
require_once '../../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header("Location: ../auth/login.php");
    exit();
}

// Get drivers from database
try {
    $stmt = $conn->prepare("
        SELECT d.*, 
               CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as driver_name,
               u.username,
               u.email,
               u.department,
               u.role
        FROM drivers d
        LEFT JOIN users u ON d.user_id = u.id
        ORDER BY d.created_at DESC
    ");
    $stmt->execute();
    $drivers = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $drivers = [];
}

// Get driver statistics
$available_count = 0;
$busy_count = 0;
$offline_count = 0;

foreach ($drivers as $driver) {
    switch ($driver['availability']) {
        case 'available':
            $available_count++;
            break;
        case 'busy':
            $busy_count++;
            break;
        case 'offline':
            $offline_count++;
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Driver Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #faf2e9;
        }

        .page-title {
            background-color: #f15b31;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }

        .card-header {
            background-color: #f15b31 !important;
            color: white !important;
        }

        .btn-primary {
            background-color: #f15b31;
            border-color: #f15b31;
        }

        .btn-primary:hover {
            background-color: #d14118;
            border-color: #d14118;
        }

        .table th {
            background-color: #f15b31;
            color: white;
        }

        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #f15b31;
        }

        .stats-card h6 {
            color: #f15b31;
            margin-bottom: 10px;
        }

        .stats-card h3 {
            color: #333;
            margin: 0;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <div class="container mt-4">
        <h1 class="page-title">🚛 Admin - Driver Management</h1>

        <!-- Back Button -->
        <div class="row mb-4">
            <div class="col-12 text-end">
                <a href="hr_reports.php" class="btn" style="background-color: #f15b31; color: white;">Back to HR Reports</a>
            </div>
        </div>

        <!-- Driver Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <h6>Available Drivers</h6>
                    <h3 style="color: #28a745;"><?php echo $available_count; ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h6>Busy Drivers</h6>
                    <h3 style="color: #ffc107;"><?php echo $busy_count; ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h6>Offline Drivers</h6>
                    <h3 style="color: #dc3545;"><?php echo $offline_count; ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <h6>Total Drivers</h6>
                    <h3><?php echo count($drivers); ?></h3>
                </div>
            </div>
        </div>

        <!-- Drivers List -->
        <div class="card mb-4">
            <div class="card-header">
                <h4>All Drivers</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Driver Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Vehicle Type</th>
                                <th>License Number</th>
                                <th>Plate Number</th>
                                <th>Availability</th>
                                <th>Department</th>
                                <th>Joined</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($drivers as $driver): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($driver['id']); ?></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars(!empty(trim($driver['driver_name'])) ? $driver['driver_name'] : $driver['username']); ?></strong>
                                    </td>
                                    <td><?php echo htmlspecialchars($driver['email'] ?? 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars($driver['phone']); ?></td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?php echo ucfirst($driver['vehicle_type']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($driver['license_number']); ?></td>
                                    <td><?php echo htmlspecialchars($driver['plate_number'] ?? 'N/A'); ?></td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $driver['availability'] == 'available' ? 'success' : 
                                                ($driver['availability'] == 'busy' ? 'warning' : 'danger'); 
                                        ?>">
                                            <?php echo ucfirst($driver['availability']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($driver['department'] ?? 'N/A'); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($driver['created_at'])); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Back to Dashboard Button -->
        <div class="text-center mt-4 mb-4">
            <a href="../admin/admin_dashboard.php" class="btn btn-primary">Back to Dashboard</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>
