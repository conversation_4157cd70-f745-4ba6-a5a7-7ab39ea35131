<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Status Check</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .status-card { margin: 15px 0; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; max-height: 200px; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">Dashboard Status Check</h1>
        
        <?php
        // Test database connection
        echo "<div class='card status-card'>";
        echo "<div class='card-header'><h3>Database Connection Test</h3></div>";
        echo "<div class='card-body'>";
        try {
            require_once 'app/config/config.php';
            echo "<div class='alert alert-success'>✅ Database connection successful!</div>";
            
            // Test inventory data
            $sql = "SELECT COUNT(*) as count FROM inventory WHERE status = 'approved'";
            $stmt = $conn->prepare($sql);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<p>Inventory items: " . $result['count'] . "</p>";
            
            // Test procurement orders
            $sql = "SELECT COUNT(*) as count FROM procurement_orders";
            $stmt = $conn->prepare($sql);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<p>Procurement orders: " . $result['count'] . "</p>";
            
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>❌ Database Error: " . $e->getMessage() . "</div>";
        }
        echo "</div></div>";
        
        // Test API endpoints
        $endpoints = [
            'app/modules/get_inventory_data.php' => 'Inventory Data API',
            'app/modules/get_low_stock.php' => 'Low Stock API',
            'includes/get_inventory_data.php' => 'Includes Inventory API',
            'includes/get_low_stock.php' => 'Includes Low Stock API'
        ];
        
        foreach ($endpoints as $endpoint => $name) {
            echo "<div class='card status-card'>";
            echo "<div class='card-header'><h3>$name</h3></div>";
            echo "<div class='card-body'>";
            
            $url = 'http://localhost/finance/' . $endpoint;
            $context = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'ignore_errors' => true
                ]
            ]);
            
            $response = @file_get_contents($url, false, $context);
            
            if ($response !== false) {
                $data = json_decode($response, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    echo "<div class='alert alert-success'>✅ Success! Retrieved " . count($data) . " records</div>";
                    if (count($data) > 0) {
                        echo "<pre>" . json_encode(array_slice($data, 0, 3), JSON_PRETTY_PRINT) . "</pre>";
                        if (count($data) > 3) {
                            echo "<p><em>... and " . (count($data) - 3) . " more records</em></p>";
                        }
                    }
                } else {
                    echo "<div class='alert alert-warning'>⚠️ JSON Error: " . json_last_error_msg() . "</div>";
                    echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
                }
            } else {
                echo "<div class='alert alert-danger'>❌ Failed to fetch from: $url</div>";
            }
            echo "</div></div>";
        }
        ?>
        
        <div class="card status-card">
            <div class="card-header"><h3>Quick Navigation</h3></div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="app/modules/admin/admin_dashboard.php" class="btn btn-primary w-100 mb-2">Admin Dashboard</a>
                    </div>
                    <div class="col-md-3">
                        <a href="app/modules/reports/reports.php" class="btn btn-success w-100 mb-2">Reports Page</a>
                    </div>
                    <div class="col-md-3">
                        <a href="app/modules/inventory/inventory_dashboard.php" class="btn btn-warning w-100 mb-2">Inventory Dashboard</a>
                    </div>
                    <div class="col-md-3">
                        <a href="verify_data.php" class="btn btn-info w-100 mb-2">Verify Data</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card status-card">
            <div class="card-header"><h3>Troubleshooting</h3></div>
            <div class="card-body">
                <h5>If you see errors:</h5>
                <ul>
                    <li><strong>404 errors:</strong> API endpoints are missing or in wrong location</li>
                    <li><strong>JSON errors:</strong> PHP errors in the API endpoints</li>
                    <li><strong>Database errors:</strong> Connection issues or missing tables</li>
                    <li><strong>JavaScript errors:</strong> Variable conflicts or missing elements</li>
                </ul>
                
                <h5>Files created/modified:</h5>
                <ul>
                    <li>✅ app/modules/get_inventory_data.php</li>
                    <li>✅ app/modules/get_low_stock.php</li>
                    <li>✅ includes/get_inventory_data.php (updated)</li>
                    <li>✅ includes/get_low_stock.php (updated)</li>
                    <li>✅ app/modules/reports/reports.php (fixed JS conflicts)</li>
                    <li>✅ Sample data inserted via add_sample_data_simple.php</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
