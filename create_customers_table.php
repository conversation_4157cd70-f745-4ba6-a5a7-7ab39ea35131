<!DOCTYPE html>
<html>
<head>
    <title>Create Customers Table</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #faf2e9; }
        .status-box { border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        .error { background: #ffebee; border-color: #f44336; }
        .warning { background: #fff3e0; border-color: #ff9800; }
        .info { background: #e3f2fd; border-color: #2196f3; }
        .btn { background: #f15b31; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
        .page-title { background-color: #f15b31; color: white; padding: 20px; border-radius: 5px; text-align: center; margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f15b31; color: white; }
        .sql-code { background: #f5f5f5; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
    </style>
</head>
<body>
    <h1 class="page-title">👥 Create Customers Table</h1>
    
    <?php
    require_once 'app/config/config.php';
    
    echo "<div class='info-box'>";
    echo "<h3>📋 Creating Customers Table</h3>";
    echo "<p>I'll create a separate customers table for better data organization...</p>";
    echo "</div>";
    
    try {
        $conn->beginTransaction();
        
        // Check if customers table already exists
        $check_table_sql = "SHOW TABLES LIKE 'customers'";
        $check_table_stmt = $conn->prepare($check_table_sql);
        $check_table_stmt->execute();
        $table_exists = $check_table_stmt->rowCount() > 0;
        
        if ($table_exists) {
            echo "<div class='warning-box'>";
            echo "<h3>⚠️ Customers Table Already Exists</h3>";
            echo "<p>The customers table already exists. I'll show you the current structure and data.</p>";
            echo "</div>";
        } else {
            // Create customers table
            echo "<div class='info-box'>";
            echo "<h3>🔧 Step 1: Creating Customers Table</h3>";
            
            $create_table_sql = "
                CREATE TABLE customers (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    customer_name VARCHAR(255) NOT NULL,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    phone VARCHAR(20),
                    address TEXT,
                    company VARCHAR(255),
                    department VARCHAR(100),
                    customer_type ENUM('individual', 'business') DEFAULT 'individual',
                    status ENUM('active', 'inactive') DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ";
            
            $conn->exec($create_table_sql);
            echo "<p style='color: green;'>✅ Customers table created successfully!</p>";
            
            echo "<div class='sql-code'>";
            echo "<strong>Table Structure:</strong><br>";
            echo "- id (Primary Key)<br>";
            echo "- customer_name (Full name)<br>";
            echo "- email (Unique email address)<br>";
            echo "- phone (Contact number)<br>";
            echo "- address (Full address)<br>";
            echo "- company (Company name if business)<br>";
            echo "- department (Department if applicable)<br>";
            echo "- customer_type (individual/business)<br>";
            echo "- status (active/inactive)<br>";
            echo "- created_at, updated_at (Timestamps)<br>";
            echo "</div>";
            echo "</div>";
        }
        
        // Insert sample customers
        echo "<div class='info-box'>";
        echo "<h3>👥 Step 2: Inserting Sample Customers</h3>";
        
        $sample_customers = [
            [
                'customer_name' => 'John Smith',
                'email' => '<EMAIL>',
                'phone' => '+63 ************',
                'address' => '123 Makati Ave, Makati City, Metro Manila',
                'company' => 'ABC Corporation',
                'department' => 'Finance',
                'customer_type' => 'business'
            ],
            [
                'customer_name' => 'Maria Garcia',
                'email' => '<EMAIL>',
                'phone' => '+63 ************',
                'address' => '456 BGC Street, Taguig City, Metro Manila',
                'company' => 'XYZ Industries',
                'department' => 'Procurement',
                'customer_type' => 'business'
            ],
            [
                'customer_name' => 'Robert Johnson',
                'email' => '<EMAIL>',
                'phone' => '+63 ************',
                'address' => '789 Ortigas Center, Pasig City, Metro Manila',
                'company' => 'DEF Logistics',
                'department' => 'Operations',
                'customer_type' => 'business'
            ],
            [
                'customer_name' => 'Lisa Chen',
                'email' => '<EMAIL>',
                'phone' => '+63 ************',
                'address' => '321 Alabang Hills, Muntinlupa City, Metro Manila',
                'company' => 'GHI Trading',
                'department' => 'Inventory',
                'customer_type' => 'business'
            ],
            [
                'customer_name' => 'David Wilson',
                'email' => '<EMAIL>',
                'phone' => '+63 ************',
                'address' => '654 Quezon City Circle, Quezon City, Metro Manila',
                'company' => 'JKL Enterprises',
                'department' => 'Sales',
                'customer_type' => 'business'
            ],
            [
                'customer_name' => 'Anna Rodriguez',
                'email' => '<EMAIL>',
                'phone' => '+63 ************',
                'address' => '987 Cebu Business Park, Cebu City',
                'company' => null,
                'department' => null,
                'customer_type' => 'individual'
            ],
            [
                'customer_name' => 'Michael Santos',
                'email' => '<EMAIL>',
                'phone' => '+63 ************',
                'address' => '147 Davao City Center, Davao City',
                'company' => null,
                'department' => null,
                'customer_type' => 'individual'
            ]
        ];
        
        $insert_sql = "INSERT INTO customers (customer_name, email, phone, address, company, department, customer_type) VALUES (?, ?, ?, ?, ?, ?, ?)";
        $insert_stmt = $conn->prepare($insert_sql);
        
        $inserted_count = 0;
        foreach ($sample_customers as $customer) {
            try {
                // Check if customer already exists
                $check_sql = "SELECT id FROM customers WHERE email = ?";
                $check_stmt = $conn->prepare($check_sql);
                $check_stmt->execute([$customer['email']]);
                
                if ($check_stmt->rowCount() == 0) {
                    $insert_stmt->execute([
                        $customer['customer_name'],
                        $customer['email'],
                        $customer['phone'],
                        $customer['address'],
                        $customer['company'],
                        $customer['department'],
                        $customer['customer_type']
                    ]);
                    echo "<p style='color: green;'>✅ Added customer: {$customer['customer_name']} ({$customer['customer_type']})</p>";
                    $inserted_count++;
                } else {
                    echo "<p style='color: blue;'>ℹ️ Customer already exists: {$customer['customer_name']}</p>";
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error adding {$customer['customer_name']}: " . $e->getMessage() . "</p>";
            }
        }
        echo "<p><strong>Total customers inserted:</strong> $inserted_count</p>";
        echo "</div>";
        
        // Update procurement_orders to reference customers table
        echo "<div class='info-box'>";
        echo "<h3>🔄 Step 3: Updating Procurement Orders to Use Customers</h3>";
        
        // Add customer_id column to procurement_orders if it doesn't exist
        $add_column_sql = "ALTER TABLE procurement_orders ADD COLUMN customer_id INT, ADD FOREIGN KEY (customer_id) REFERENCES customers(id)";
        try {
            $conn->exec($add_column_sql);
            echo "<p style='color: green;'>✅ Added customer_id column to procurement_orders</p>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<p style='color: blue;'>ℹ️ customer_id column already exists in procurement_orders</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ Note: " . $e->getMessage() . "</p>";
            }
        }
        
        // Update existing orders to link with customers
        $update_orders_sql = "
            UPDATE procurement_orders po 
            JOIN customers c ON po.customer_email = c.email 
            SET po.customer_id = c.id 
            WHERE po.customer_id IS NULL
        ";
        $update_stmt = $conn->prepare($update_orders_sql);
        $update_stmt->execute();
        $updated_orders = $update_stmt->rowCount();
        echo "<p style='color: green;'>✅ Updated $updated_orders orders to link with customers</p>";
        echo "</div>";
        
        $conn->commit();
        
        // Show final customers table
        echo "<div class='success-box'>";
        echo "<h3>👥 Final Customers Table</h3>";
        
        $customers_sql = "SELECT * FROM customers ORDER BY customer_type, customer_name";
        $customers_stmt = $conn->prepare($customers_sql);
        $customers_stmt->execute();
        $customers = $customers_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Phone</th><th>Company</th><th>Type</th><th>Status</th></tr>";
        foreach ($customers as $customer) {
            echo "<tr>";
            echo "<td>" . $customer['id'] . "</td>";
            echo "<td>" . htmlspecialchars($customer['customer_name']) . "</td>";
            echo "<td>" . htmlspecialchars($customer['email']) . "</td>";
            echo "<td>" . htmlspecialchars($customer['phone'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($customer['company'] ?? 'Individual') . "</td>";
            echo "<td>" . ucfirst($customer['customer_type']) . "</td>";
            echo "<td>" . ucfirst($customer['status']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p><strong>Total customers:</strong> " . count($customers) . "</p>";
        echo "</div>";
        
    } catch (Exception $e) {
        $conn->rollback();
        echo "<div class='error-box'>";
        echo "<h3>❌ Error Creating Customers Table</h3>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    echo "<div class='info-box'>";
    echo "<h3>🎯 Next Steps</h3>";
    echo "<p>Now I need to update the reports to use the customers table instead of users table.</p>";
    echo "<a href='update_reports_for_customers.php' class='btn'>🔄 Update Reports for Customers</a>";
    echo "<a href='http://localhost/phpmyadmin' class='btn' target='_blank'>🔍 View in phpMyAdmin</a>";
    echo "</div>";
    
    echo "<div class='success-box'>";
    echo "<h3>✅ Customers Table Created!</h3>";
    echo "<p>Benefits of separate customers table:</p>";
    echo "<ul>";
    echo "<li>✅ Better data organization</li>";
    echo "<li>✅ Separate business and individual customers</li>";
    echo "<li>✅ Complete customer information (phone, address, company)</li>";
    echo "<li>✅ Proper foreign key relationships</li>";
    echo "<li>✅ Customer status management</li>";
    echo "</ul>";
    echo "</div>";
    ?>
    
    <script>
        console.log('👥 Customers table created successfully');
    </script>
</body>
</html>
