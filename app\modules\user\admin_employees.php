<?php
session_start();
require_once '../../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header("Location: ../auth/login.php");
    exit();
}

// Create employee_performance table if it doesn't exist
try {
    $conn->exec("CREATE TABLE IF NOT EXISTS employee_performance (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        department_id INT,
        evaluation_date DATE NOT NULL,
        performance_score DECIMAL(3,2) NOT NULL,
        goals_achieved INT DEFAULT 0,
        total_goals INT DEFAULT 0,
        attendance_rate DECIMAL(5,2) DEFAULT 100.00,
        supervisor_feedback TEXT,
        status ENUM('draft', 'completed', 'approved', 'needs_improvement') DEFAULT 'draft',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
} catch (PDOException $e) {
    // Table creation failed, but continue
}

// Just create the table, no sample data insertion

// Get all users from database
try {
    $stmt = $conn->prepare("SELECT * FROM users ORDER BY created_at DESC");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $users = [];
}

// Get all departments from database
try {
    $stmt = $conn->prepare("SELECT * FROM departments ORDER BY name");
    $stmt->execute();
    $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $departments = [];
}

// Get all roles from database
try {
    $stmt = $conn->prepare("SELECT * FROM roles ORDER BY name");
    $stmt->execute();
    $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $roles = [];
}

// Get payroll data as performance indicator
try {
    $stmt = $conn->prepare("
        SELECT p.*,
               CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as employee_name,
               u.username,
               u.department,
               u.role
        FROM payroll p
        LEFT JOIN users u ON p.user_id = u.id
        ORDER BY p.payment_date DESC
        LIMIT 20
    ");
    $stmt->execute();
    $payroll_records = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $payroll_records = [];
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Employee Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #faf2e9;
        }

        .page-title {
            background-color: #f15b31;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }

        .card-header {
            background-color: #f15b31 !important;
            color: white !important;
        }

        .btn-primary {
            background-color: #f15b31;
            border-color: #f15b31;
        }

        .btn-primary:hover {
            background-color: #d14118;
            border-color: #d14118;
        }

        .table th {
            background-color: #f15b31;
            color: white;
        }
    </style>
</head>

<body>
    <div class="container mt-4">
        <h1 class="page-title">👥 Admin - Employee Management</h1>

        <!-- Back Button -->
        <div class="row mb-4">
            <div class="col-12 text-end">
                <a href="hr_reports.php" class="btn" style="background-color: #f15b31; color: white;">Back to HR Reports</a>
            </div>
        </div>

        <!-- Employees List -->
        <div class="card mb-4">
            <div class="card-header">
                <h4>All Employees</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Department</th>
                                <th>Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($user['id']); ?></td>
                                    <td><?php echo htmlspecialchars(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '')); ?></td>
                                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td><?php echo htmlspecialchars($user['role']); ?></td>
                                    <td><?php echo htmlspecialchars($user['department']); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Departments List -->
        <div class="card mb-4">
            <div class="card-header">
                <h4>All Departments</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Department Name</th>
                                <th>Description</th>
                                <th>Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($departments as $dept): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($dept['id']); ?></td>
                                    <td><?php echo htmlspecialchars($dept['name']); ?></td>
                                    <td><?php echo htmlspecialchars($dept['description'] ?? 'N/A'); ?></td>
                                    <td><?php echo isset($dept['created_at']) ? date('M d, Y', strtotime($dept['created_at'])) : 'N/A'; ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Roles List -->
        <div class="card mb-4">
            <div class="card-header">
                <h4>All Roles</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Role Name</th>
                                <th>Description</th>
                                <th>Permissions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($roles as $role): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($role['id']); ?></td>
                                    <td><?php echo htmlspecialchars($role['name']); ?></td>
                                    <td><?php echo htmlspecialchars($role['description'] ?? 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars($role['permissions'] ?? 'N/A'); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Payroll Records -->
        <div class="card mb-4">
            <div class="card-header">
                <h4>Recent Payroll Records</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Employee</th>
                                <th>Department</th>
                                <th>Role</th>
                                <th>Pay Rate</th>
                                <th>Hours Worked</th>
                                <th>Gross Pay</th>
                                <th>Net Pay</th>
                                <th>Payment Date</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($payroll_records as $record): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($record['id']); ?></td>
                                    <td><?php echo htmlspecialchars(!empty(trim($record['employee_name'])) ? $record['employee_name'] : $record['username']); ?></td>
                                    <td><?php echo htmlspecialchars($record['department'] ?? 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars($record['role'] ?? 'N/A'); ?></td>
                                    <td>₱<?php echo number_format($record['pay_rate'], 2); ?></td>
                                    <td><?php echo number_format($record['hours_worked'], 1); ?> hrs</td>
                                    <td>₱<?php echo number_format($record['gross_pay'], 2); ?></td>
                                    <td>₱<?php echo number_format($record['net_pay'], 2); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($record['payment_date'])); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $record['status'] == 'processed' ? 'success' : 'warning'; ?>">
                                            <?php echo ucfirst($record['status']); ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Back to Dashboard Button -->
        <div class="text-center mt-4 mb-4">
            <a href="../admin/admin_dashboard.php" class="btn btn-primary">Back to Dashboard</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>