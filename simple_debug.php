<!DOCTYPE html>
<html>
<head>
    <title>Simple Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .box { border: 1px solid #ccc; padding: 15px; margin: 10px 0; }
        .error { background: #ffebee; border-color: #f44336; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        .warning { background: #fff3e0; border-color: #ff9800; }
    </style>
</head>
<body>
    <h1>🔍 Simple Dashboard Debug</h1>
    
    <?php
    echo "<div class='box'>";
    echo "<h3>1. Database Connection Test</h3>";
    
    try {
        require_once 'app/config/config.php';
        echo "<p class='success'>✅ Database connected successfully</p>";
        
        // Test basic queries
        $tables = ['users', 'products', 'inventory', 'procurement_orders'];
        foreach ($tables as $table) {
            try {
                $stmt = $conn->prepare("SELECT COUNT(*) as count FROM $table");
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                echo "<p>📊 $table: " . $result['count'] . " records</p>";
            } catch (Exception $e) {
                echo "<p class='error'>❌ $table: " . $e->getMessage() . "</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    echo "<div class='box'>";
    echo "<h3>2. Sales Data Test</h3>";
    
    if (isset($conn)) {
        try {
            // Simple sales query
            $sql = "SELECT COUNT(*) as orders, SUM(total_amount) as total FROM procurement_orders WHERE status = 'completed'";
            $stmt = $conn->prepare($sql);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo "<p>📈 Completed Orders: " . $result['orders'] . "</p>";
            echo "<p>💰 Total Sales: $" . number_format($result['total'], 2) . "</p>";
            
            if ($result['total'] > 0) {
                echo "<p class='success'>✅ Sales data exists!</p>";
            } else {
                echo "<p class='warning'>⚠️ No sales data found</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Sales query failed: " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
    
    echo "<div class='box'>";
    echo "<h3>3. Inventory Data Test</h3>";
    
    if (isset($conn)) {
        try {
            $sql = "SELECT brand_name, COUNT(*) as products, SUM(stocks) as total_stock FROM inventory WHERE status = 'approved' GROUP BY brand_name";
            $stmt = $conn->prepare($sql);
            $stmt->execute();
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($results) > 0) {
                echo "<p class='success'>✅ Inventory data found:</p>";
                foreach ($results as $row) {
                    echo "<p>🏷️ " . $row['brand_name'] . ": " . $row['products'] . " products, " . $row['total_stock'] . " stock</p>";
                }
            } else {
                echo "<p class='warning'>⚠️ No inventory data found</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Inventory query failed: " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
    
    echo "<div class='box'>";
    echo "<h3>4. API Test</h3>";
    
    $api_url = 'http://localhost/finance/app/modules/get_inventory_data.php';
    $response = @file_get_contents($api_url);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "<p class='success'>✅ API working: " . count($data) . " records</p>";
            echo "<pre>" . substr($response, 0, 200) . "...</pre>";
        } else {
            echo "<p class='error'>❌ API JSON error: " . json_last_error_msg() . "</p>";
            echo "<pre>" . htmlspecialchars(substr($response, 0, 200)) . "</pre>";
        }
    } else {
        echo "<p class='error'>❌ API not accessible at: $api_url</p>";
    }
    echo "</div>";
    
    echo "<div class='box'>";
    echo "<h3>5. Current Dashboard Status</h3>";
    echo "<p>🔗 <a href='app/modules/inventory/inventory_dashboard.php' target='_blank'>Open Inventory Dashboard</a></p>";
    echo "<p>🔗 <a href='app/modules/admin/admin_dashboard.php' target='_blank'>Open Admin Dashboard</a></p>";
    echo "<p>📝 Tell me exactly what you see when you open these links</p>";
    echo "</div>";
    
    echo "<div class='box'>";
    echo "<h3>6. Quick Fix Options</h3>";
    echo "<p>If data exists but dashboard still shows zeros:</p>";
    echo "<p>🔧 <a href='add_sample_data_simple.php' target='_blank'>Re-run Data Insert</a></p>";
    echo "<p>🔧 <a href='verify_data.php' target='_blank'>Verify All Data</a></p>";
    echo "</div>";
    ?>
    
    <script>
        console.log('🔍 Debug page loaded');
        
        // Test API from JavaScript
        fetch('app/modules/get_inventory_data.php')
            .then(response => response.json())
            .then(data => {
                console.log('✅ API test from JS:', data);
            })
            .catch(error => {
                console.error('❌ API test failed:', error);
            });
    </script>
</body>
</html>
