<?php
echo "<h2>Debug Inventory API</h2>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

// Test direct database query
echo "<div class='section'>";
echo "<h3>1. Direct Database Query Test</h3>";
try {
    require_once 'app/config/config.php';
    
    // Test inventory query
    $sql = "SELECT brand_name, SUM(stocks) as total_stock, COUNT(*) as total_products FROM inventory WHERE status = 'approved' GROUP BY brand_name ORDER BY total_stock DESC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div class='success'>";
    echo "<p>✅ Direct database query successful!</p>";
    echo "<p>Found " . count($result) . " brand records</p>";
    echo "<pre>" . json_encode($result, JSON_PRETTY_PRINT) . "</pre>";
    echo "</div>";
    
    // Test individual table counts
    echo "<h4>Table Counts:</h4>";
    
    $tables = ['users', 'products', 'inventory', 'departments', 'categories'];
    foreach ($tables as $table) {
        $sql = "SELECT COUNT(*) as count FROM $table";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "<p>$table: $count records</p>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<p>❌ Database Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}
echo "</div>";

// Test API endpoints
echo "<div class='section'>";
echo "<h3>2. API Endpoint Tests</h3>";

$endpoints = [
    'app/modules/get_inventory_data.php',
    'includes/get_inventory_data.php'
];

foreach ($endpoints as $endpoint) {
    echo "<h4>Testing: $endpoint</h4>";
    
    $url = 'http://localhost/finance/' . $endpoint;
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'ignore_errors' => true
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "<div class='success'>";
            echo "<p>✅ Success! Retrieved " . count($data) . " records</p>";
            echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT) . "</pre>";
            echo "</div>";
        } else {
            echo "<div class='error'>";
            echo "<p>❌ JSON Error: " . json_last_error_msg() . "</p>";
            echo "<p>Raw response:</p>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
            echo "</div>";
        }
    } else {
        echo "<div class='error'>";
        echo "<p>❌ Failed to fetch from: $url</p>";
        echo "</div>";
    }
}
echo "</div>";

// Test JavaScript fetch simulation
echo "<div class='section'>";
echo "<h3>3. JavaScript Fetch Simulation</h3>";
echo "<div id='fetchTest'></div>";
echo "<script>
async function testFetch() {
    const testDiv = document.getElementById('fetchTest');
    
    try {
        const response = await fetch('app/modules/get_inventory_data.php');
        const data = await response.json();
        
        testDiv.innerHTML = `
            <div class='success'>
                <p>✅ JavaScript fetch successful!</p>
                <p>Retrieved \${data.length} records</p>
                <pre>\${JSON.stringify(data, null, 2)}</pre>
            </div>
        `;
    } catch (error) {
        testDiv.innerHTML = `
            <div class='error'>
                <p>❌ JavaScript fetch error: \${error.message}</p>
            </div>
        `;
    }
}

testFetch();
</script>";
echo "</div>";

echo "<div class='section'>";
echo "<h3>4. Quick Links</h3>";
echo "<p><a href='app/modules/admin/admin_dashboard.php'>Admin Dashboard</a></p>";
echo "<p><a href='app/modules/inventory/inventory_dashboard.php'>Inventory Dashboard</a></p>";
echo "<p><a href='dashboard_status_check.php'>Status Check</a></p>";
echo "</div>";
?>
